#!/usr/bin/env python3
"""
测试律师推荐系统
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
from api.lawyer_data_api import LawyerDataAPI
from utils.web_search import WebSearchEngine
from utils.logger import LOG

def test_web_search_engine():
    """测试网络搜索引擎"""
    print("\n=== 测试网络搜索引擎 ===")
    
    try:
        search_engine = WebSearchEngine()
        
        # 测试搜索律师事务所
        results = search_engine.search_lawyers("北京律师事务所", max_results=5)
        
        print(f"搜索结果数量: {len(results)}")
        for i, result in enumerate(results[:3], 1):
            print(f"{i}. {result.get('title', 'N/A')}")
            print(f"   描述: {result.get('description', 'N/A')[:100]}...")
            print(f"   来源: {result.get('source', 'N/A')}")
            print()
        
        # 测试提取律师信息
        lawyer_firms = search_engine.extract_lawyer_info(results)
        print(f"提取到的律师事务所数量: {len(lawyer_firms)}")
        
        for i, firm in enumerate(lawyer_firms[:2], 1):
            print(f"{i}. {firm.get('name', 'N/A')}")
            print(f"   地点: {firm.get('location', 'N/A')}")
            print(f"   专业: {', '.join(firm.get('specialties', []))}")
            print(f"   评分: {firm.get('rating', 'N/A')}")
            print()
        
        return True
        
    except Exception as e:
        print(f"网络搜索引擎测试失败: {str(e)}")
        return False

def test_lawyer_data_api():
    """测试律师数据API"""
    print("\n=== 测试律师数据API ===")
    
    try:
        api = LawyerDataAPI()
        
        # 测试获取律师数据
        lawyers = api.fetch_lawyers_from_api()
        
        print(f"获取到的律师团队数量: {len(lawyers)}")
        
        for i, lawyer in enumerate(lawyers[:3], 1):
            print(f"{i}. {lawyer.get('name', 'N/A')}")
            print(f"   地点: {lawyer.get('location', 'N/A')}")
            print(f"   专业: {', '.join(lawyer.get('specialties', []))}")
            print(f"   评分: {lawyer.get('rating', 'N/A')}")
            print(f"   来源: {lawyer.get('source', 'N/A')}")
            print()
        
        # 测试专业搜索
        specialty_lawyers = api.search_lawyers_by_specialty("婚姻家庭", "北京")
        print(f"专业搜索结果数量: {len(specialty_lawyers)}")
        
        return True
        
    except Exception as e:
        print(f"律师数据API测试失败: {str(e)}")
        return False

def test_lawyer_recommendation_agent():
    """测试律师推荐代理"""
    print("\n=== 测试律师推荐代理 ===")
    
    try:
        agent = LawyerRecommendationAgent()
        
        # 测试推荐请求
        test_requests = [
            "我需要处理离婚财产分割问题，希望找北京的专业律师",
            "公司合同纠纷，需要上海的律师团队",
            "工伤赔偿案件，寻找有经验的劳动法律师",
            "知识产权侵权诉讼，需要专业律师"
        ]
        
        for i, request in enumerate(test_requests, 1):
            print(f"\n测试请求 {i}: {request}")
            
            try:
                response = agent.process_recommendation_request(request, f"test_session_{i}")
                
                # 检查响应是否包含推荐结果
                if "律师团队推荐结果" in response:
                    print("✓ 成功生成推荐结果")
                    # 显示部分结果
                    lines = response.split('<br>')
                    for line in lines[:10]:  # 显示前10行
                        if line.strip():
                            print(f"  {line.strip()}")
                    if len(lines) > 10:
                        print("  ...")
                else:
                    print("✗ 推荐结果格式异常")
                    print(f"  响应: {response[:200]}...")
                
            except Exception as e:
                print(f"✗ 处理请求失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"律师推荐代理测试失败: {str(e)}")
        return False

def test_data_quality():
    """测试数据质量"""
    print("\n=== 测试数据质量 ===")
    
    try:
        agent = LawyerRecommendationAgent()
        lawyers = agent.load_lawyer_teams()
        
        print(f"总律师团队数量: {len(lawyers)}")
        
        # 检查数据完整性
        complete_data_count = 0
        for lawyer in lawyers:
            if (lawyer.get('name') and 
                lawyer.get('location') and 
                lawyer.get('specialties') and 
                lawyer.get('description') and
                lawyer.get('contact')):
                complete_data_count += 1
        
        print(f"完整数据的律师团队: {complete_data_count}/{len(lawyers)}")
        print(f"数据完整率: {complete_data_count/len(lawyers)*100:.1f}%")
        
        # 检查数据来源
        sources = {}
        for lawyer in lawyers:
            source = lawyer.get('source', '未知')
            sources[source] = sources.get(source, 0) + 1
        
        print("\n数据来源分布:")
        for source, count in sources.items():
            print(f"  {source}: {count} 个")
        
        return True
        
    except Exception as e:
        print(f"数据质量测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的律师推荐系统...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("网络搜索引擎", test_web_search_engine()))
    test_results.append(("律师数据API", test_lawyer_data_api()))
    test_results.append(("律师推荐代理", test_lawyer_recommendation_agent()))
    test_results.append(("数据质量", test_data_quality()))
    
    # 汇总测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！律师推荐系统修复成功。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
