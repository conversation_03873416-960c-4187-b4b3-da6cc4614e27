2025-07-16 22:20:12 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 1, 'begin_time': 1660, 'end_time': 4120, 'text': '好的好的。', 'channel_id': 0, 'speaker_id': None, 'sentence_end': True, 'heartbeat': False, 'words': [{'begin_time': 1660, 'end_time': 2890, 'text': '好的', 'punctuation': '', 'fixed': False, 'speaker_id': None}, {'begin_time': 2890, 'end_time': 4120, 'text': '好的', 'punctuation': '。', 'fixed': False, 'speaker_id': None}], 'emo_tag': 'neutral', 'emo_confidence': 0.925, 'stash': {'sentence_id': 2, 'text': '', 'begin_time': 4120, 'current_time': 4120, 'words': []}}
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:353 - <PERSON><PERSON><PERSON> ended, accumulated text: 好的好的。
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 2, 'begin_time': 5180, 'end_time': None, 'text': '你', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 5180, 'end_time': 5760, 'text': '你', 'punctuation': '', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:356 - Partial sentence: 你
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 2, 'begin_time': 5180, 'end_time': None, 'text': '没，', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 5180, 'end_time': 6160, 'text': '没', 'punctuation': '，', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:356 - Partial sentence: 没，
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 2, 'begin_time': 5180, 'end_time': None, 'text': '没问题', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 5180, 'end_time': 5950, 'text': '没问', 'punctuation': '', 'fixed': False, 'speaker_id': None}, {'begin_time': 5950, 'end_time': 6720, 'text': '题', 'punctuation': '', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:356 - Partial sentence: 没问题
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 2, 'begin_time': 5180, 'end_time': 6779, 'text': '没问题。', 'channel_id': 0, 'speaker_id': None, 'sentence_end': True, 'heartbeat': False, 'words': [{'begin_time': 5180, 'end_time': 5979, 'text': '没问', 'punctuation': '', 'fixed': False, 'speaker_id': None}, {'begin_time': 5979, 'end_time': 6779, 'text': '题', 'punctuation': '。', 'fixed': False, 'speaker_id': None}], 'emo_tag': 'neutral', 'emo_confidence': 0.691, 'stash': {'sentence_id': 3, 'text': '', 'begin_time': 6779, 'current_time': 6779, 'words': []}}
2025-07-16 22:20:12 | DEBUG | agent_base:on_event:353 - Sentence ended, accumulated text: 好的好的。 没问题。
2025-07-16 22:20:12 | DEBUG | agent_base:_try_speech_recognition:449 - Final recognition result: '好的好的。 没问题。'
2025-07-16 22:20:14 | DEBUG | agent_base:_analyze_audio_data:757 - Audio data analysis - Format: webm, Size: 10788, Header: 1a45dfa39f4286810142f7810142f281
2025-07-16 22:20:14 | DEBUG | agent_base:_analyze_audio_data:763 - Detected WebM/Matroska container
2025-07-16 22:20:15 | DEBUG | agent_base:_find_ffmpeg:665 - Found ffmpeg at: F:\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-16 22:20:16 | DEBUG | agent_base:_try_speech_recognition:412 - Sent 4 audio chunks to paraformer-realtime-8k-v2
2025-07-16 22:20:16 | DEBUG | agent_base:_try_speech_recognition:449 - Final recognition result: 'None'
2025-07-16 22:20:16 | ERROR | agent_base:speech_to_text:278 - All speech recognition attempts failed
2025-07-16 22:20:17 | DEBUG | agent_base:_analyze_audio_data:757 - Audio data analysis - Format: webm, Size: 26243, Header: 1a45dfa39f4286810142f7810142f281
2025-07-16 22:20:17 | DEBUG | agent_base:_analyze_audio_data:763 - Detected WebM/Matroska container
2025-07-16 22:20:17 | DEBUG | agent_base:_find_ffmpeg:665 - Found ffmpeg at: F:\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-16 22:20:18 | DEBUG | agent_base:_try_speech_recognition:412 - Sent 9 audio chunks to paraformer-realtime-8k-v2
2025-07-16 22:20:18 | DEBUG | agent_base:_try_speech_recognition:449 - Final recognition result: 'None'
2025-07-16 22:20:18 | ERROR | agent_base:speech_to_text:278 - All speech recognition attempts failed
2025-07-16 22:21:40 | DEBUG | agent_base:_analyze_audio_data:757 - Audio data analysis - Format: webm, Size: 160517, Header: 1a45dfa39f4286810142f7810142f281
2025-07-16 22:21:40 | DEBUG | agent_base:_analyze_audio_data:763 - Detected WebM/Matroska container
2025-07-16 22:21:40 | DEBUG | agent_base:_find_ffmpeg:665 - Found ffmpeg at: F:\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-16 22:21:41 | DEBUG | agent_base:_try_speech_recognition:412 - Sent 50 audio chunks to paraformer-realtime-8k-v2
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 1, 'begin_time': 1340, 'end_time': None, 'text': '好', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 1340, 'end_time': 1920, 'text': '好', 'punctuation': '', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:356 - Partial sentence: 好
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 1, 'begin_time': 1340, 'end_time': None, 'text': '好的', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 1340, 'end_time': 2320, 'text': '好的', 'punctuation': '', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:356 - Partial sentence: 好的
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 1, 'begin_time': 1340, 'end_time': None, 'text': '好的好的', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 1340, 'end_time': 2420, 'text': '好的', 'punctuation': '', 'fixed': False, 'speaker_id': None}, {'begin_time': 2420, 'end_time': 3500, 'text': '好的', 'punctuation': '', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:356 - Partial sentence: 好的好的
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 1, 'begin_time': 1340, 'end_time': 3540, 'text': '好的好的。', 'channel_id': 0, 'speaker_id': None, 'sentence_end': True, 'heartbeat': False, 'words': [{'begin_time': 1340, 'end_time': 2440, 'text': '好的', 'punctuation': '', 'fixed': False, 'speaker_id': None}, {'begin_time': 2440, 'end_time': 3540, 'text': '好的', 'punctuation': '。', 'fixed': False, 'speaker_id': None}], 'emo_tag': 'neutral', 'emo_confidence': 0.828, 'stash': {'sentence_id': 2, 'text': '', 'begin_time': 3540, 'current_time': 3540, 'words': []}}
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:353 - Sentence ended, accumulated text: 好的好的。
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 2, 'begin_time': 4220, 'end_time': None, 'text': '没，', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 4220, 'end_time': 4720, 'text': '没', 'punctuation': '，', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:356 - Partial sentence: 没，
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 2, 'begin_time': 4220, 'end_time': None, 'text': '没问', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 4220, 'end_time': 5120, 'text': '没问', 'punctuation': '', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:356 - Partial sentence: 没问
2025-07-16 22:21:41 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 2, 'begin_time': 4220, 'end_time': 5639, 'text': '没问题。', 'channel_id': 0, 'speaker_id': None, 'sentence_end': True, 'heartbeat': False, 'words': [{'begin_time': 4220, 'end_time': 4929, 'text': '没问', 'punctuation': '', 'fixed': False, 'speaker_id': None}, {'begin_time': 4929, 'end_time': 5639, 'text': '题', 'punctuation': '。', 'fixed': False, 'speaker_id': None}], 'emo_tag': 'neutral', 'emo_confidence': 0.846, 'stash': {'sentence_id': 3, 'text': '', 'begin_time': 5639, 'current_time': 5639, 'words': []}}
2025-07-16 22:21:42 | DEBUG | agent_base:on_event:353 - Sentence ended, accumulated text: 好的好的。 没问题。
2025-07-16 22:21:42 | DEBUG | agent_base:_try_speech_recognition:449 - Final recognition result: '好的好的。 没问题。'
2025-07-16 22:21:44 | DEBUG | agent_base:_analyze_audio_data:757 - Audio data analysis - Format: webm, Size: 21413, Header: 1a45dfa39f4286810142f7810142f281
2025-07-16 22:21:44 | DEBUG | agent_base:_analyze_audio_data:763 - Detected WebM/Matroska container
2025-07-16 22:21:44 | DEBUG | agent_base:_find_ffmpeg:665 - Found ffmpeg at: F:\ffmpeg-7.0.2-essentials_build\bin\ffmpeg.exe
2025-07-16 22:21:45 | DEBUG | agent_base:_try_speech_recognition:412 - Sent 7 audio chunks to paraformer-realtime-8k-v2
2025-07-16 22:21:45 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 1, 'begin_time': 0, 'end_time': None, 'text': '现', 'channel_id': 0, 'speaker_id': None, 'sentence_end': False, 'heartbeat': False, 'words': [{'begin_time': 0, 'end_time': 560, 'text': '现', 'punctuation': '', 'fixed': False, 'speaker_id': None}]}
2025-07-16 22:21:45 | DEBUG | agent_base:on_event:356 - Partial sentence: 现
2025-07-16 22:21:45 | DEBUG | agent_base:on_event:339 - Received sentence: {'sentence_id': 1, 'begin_time': 0, 'end_time': 1300, 'text': '谢谢。', 'channel_id': 0, 'speaker_id': None, 'sentence_end': True, 'heartbeat': False, 'words': [{'begin_time': 0, 'end_time': 1300, 'text': '谢谢', 'punctuation': '。', 'fixed': False, 'speaker_id': None}], 'emo_tag': 'neutral', 'emo_confidence': 0.0, 'stash': {'sentence_id': 2, 'text': '', 'begin_time': 1300, 'current_time': 1300, 'words': []}}
2025-07-16 22:21:45 | DEBUG | agent_base:on_event:353 - Sentence ended, accumulated text: 谢谢。
2025-07-16 22:21:45 | DEBUG | agent_base:_try_speech_recognition:449 - Final recognition result: '谢谢。'
2025-07-16 22:21:47 | DEBUG | agent_base:chat_with_history:169 - [ChatBot][conversation] 不客气！很高兴能为你提供帮助。如果你有任何法律问题，随时可以告诉我，我会尽力为你解答。

如果有任何不确定的地方，或者需要进一步了解的法律内容，也欢迎继续提问。祝你一切顺利！😊
2025-07-16 22:21:55 | DEBUG | agent_base:text_to_speech:818 - Speech synthesis successful, audio size: 227832 bytes
2025-07-16 22:21:55 | DEBUG | app:conversation_chat:201 - TTS成功，音频大小: 227832 bytes
