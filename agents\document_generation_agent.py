import os
import json
from typing import Dict, List, Any, Optional
from pathlib import Path
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
import tempfile
import uuid
from datetime import datetime

from .agent_base import AgentBase
from utils.logger import LOG


class DocumentGenerationAgent(AgentBase):
    """文书生成智能体"""
    
    def __init__(self):
        super().__init__(
            name="document_generation",
            prompt_file="prompts/document_generation_prompt.txt"
        )
        self.generated_docs_dir = Path(__file__).parent.parent / "generated_docs"
        self.generated_docs_dir.mkdir(exist_ok=True)

        # 模板信息映射 - 使用代码生成专业法律文书
        self.template_info = {
            "house_lease": {
                "name": "房屋租赁纠纷起诉状",
                "description": "用于房屋租赁合同纠纷的民事起诉状",
                "category": "民事诉讼",
                "required_fields": [
                    "当事人姓名", "性别", "年龄", "联系电话", "地址"
                ]
            },
            "traffic_accident": {
                "name": "交通事故损害赔偿起诉状",
                "description": "用于交通事故损害赔偿的民事起诉状",
                "category": "民事诉讼",
                "required_fields": [
                    "当事人姓名", "性别", "年龄", "联系电话", "地址"
                ]
            },
            "sales_contract": {
                "name": "买卖合同纠纷起诉状",
                "description": "用于买卖合同纠纷的民事起诉状",
                "category": "民事诉讼",
                "required_fields": [
                    "当事人姓名", "性别", "年龄", "联系电话", "地址"
                ]
            },
            "divorce_agreement": {
                "name": "离婚协议书",
                "description": "用于协议离婚的离婚协议书",
                "category": "婚姻家庭",
                "required_fields": [
                    "当事人姓名", "性别", "年龄", "联系电话", "地址"
                ]
            },
            "loan_dispute": {
                "name": "借款纠纷起诉状",
                "description": "用于借款合同纠纷的民事起诉状",
                "category": "民事诉讼",
                "required_fields": [
                    "当事人姓名", "性别", "年龄", "联系电话", "地址"
                ]
            },
            "labor_arbitration": {
                "name": "劳动仲裁申请书",
                "description": "用于劳动争议仲裁的申请书",
                "category": "劳动争议",
                "required_fields": [
                    "当事人姓名", "性别", "年龄", "联系电话", "地址"
                ]
            }
        }
        
        # 当前会话状态
        self.session_states = {}
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """获取可用的模板列表 - 使用代码生成专业法律文书"""
        templates = []

        for template_id, info in self.template_info.items():
            templates.append({
                "id": template_id,
                "name": info["name"],
                "description": info["description"],
                "category": info["category"],
                "filename": f"{info['name']}.docx"
            })

        return templates
    
    def start_document_generation(self, template_id: str, session_id: str) -> str:
        """开始文书生成流程"""
        if template_id not in self.template_info:
            return "抱歉，未找到指定的模板。"
        
        template_info = self.template_info[template_id]
        
        # 初始化会话状态
        self.session_states[session_id] = {
            "template_id": template_id,
            "template_info": template_info,
            "collected_fields": {},
            "current_field_index": 0,
            "status": "collecting"
        }
        
        # 开始收集第一个字段
        return self._ask_next_field(session_id)
    
    def process_user_input(self, user_input: str, session_id: str) -> str:
        """处理用户输入"""
        if session_id not in self.session_states:
            return "请先选择要生成的文书模板。"
        
        session_state = self.session_states[session_id]
        
        if session_state["status"] == "collecting":
            return self._collect_field_info(user_input, session_id)
        elif session_state["status"] == "confirming":
            return self._handle_confirmation(user_input, session_id)
        else:
            return "当前状态异常，请重新开始。"
    
    def _ask_next_field(self, session_id: str) -> str:
        """询问下一个字段信息"""
        session_state = self.session_states[session_id]
        required_fields = session_state["template_info"]["required_fields"]
        current_index = session_state["current_field_index"]
        
        if current_index >= len(required_fields):
            # 所有字段收集完毕，进入确认阶段
            return self._show_confirmation(session_id)
        
        field_name = required_fields[current_index]
        
        # 根据字段类型提供不同的提示
        field_prompts = {
            "当事人姓名": "请提供您的姓名：",
            "性别": "请提供您的性别（男/女）：",
            "年龄": "请提供您的年龄：",
            "联系电话": "请提供您的联系电话：",
            "地址": "请提供您的详细地址："
        }
        
        # 查找匹配的提示
        prompt = field_prompts.get(field_name, f"请提供{field_name}：")
        
        return f"📝 正在收集信息 ({current_index + 1}/{len(required_fields)})\n\n{prompt}"
    
    def _collect_field_info(self, user_input: str, session_id: str) -> str:
        """收集字段信息"""
        session_state = self.session_states[session_id]
        required_fields = session_state["template_info"]["required_fields"]
        current_index = session_state["current_field_index"]
        
        field_name = required_fields[current_index]
        session_state["collected_fields"][field_name] = user_input.strip()
        session_state["current_field_index"] += 1
        
        return self._ask_next_field(session_id)
    
    def _show_confirmation(self, session_id: str) -> str:
        """显示确认信息"""
        session_state = self.session_states[session_id]
        session_state["status"] = "confirming"
        
        template_name = session_state["template_info"]["name"]
        collected_fields = session_state["collected_fields"]
        
        confirmation_text = f"✅ 信息收集完成！\n\n📋 **{template_name}** 的信息如下：\n\n"
        
        for field_name, value in collected_fields.items():
            confirmation_text += f"**{field_name}：** {value}\n"
        
        confirmation_text += "\n请确认以上信息是否正确？\n"
        confirmation_text += "- 回复 **确认** 或 **是** 来生成文书\n"
        confirmation_text += "- 回复 **修改** 来重新填写信息\n"
        confirmation_text += "- 回复 **取消** 来取消生成"
        
        return confirmation_text
    
    def _handle_confirmation(self, user_input: str, session_id: str) -> str:
        """处理确认响应"""
        user_input = user_input.strip().lower()
        
        if user_input in ["确认", "是", "yes", "y", "确定", "生成"]:
            return self._generate_document(session_id)
        elif user_input in ["修改", "重新填写", "重填"]:
            # 重新开始收集信息
            session_state = self.session_states[session_id]
            session_state["collected_fields"] = {}
            session_state["current_field_index"] = 0
            session_state["status"] = "collecting"
            return "好的，让我们重新填写信息。\n\n" + self._ask_next_field(session_id)
        elif user_input in ["取消", "cancel", "不生成"]:
            del self.session_states[session_id]
            return "已取消文书生成。如需重新生成，请选择模板重新开始。"
        else:
            return "请回复 **确认**、**修改** 或 **取消**。"

    def _generate_document(self, session_id: str) -> Dict[str, Any]:
        """基于模板复制和修改生成文档"""
        try:
            session_state = self.session_states[session_id]
            template_id = session_state["template_id"]
            collected_fields = session_state["collected_fields"]

            # 直接基于模板ID创建专业法律文书
            doc = self._create_document_by_template_id(template_id, collected_fields)

            # 生成唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            template_name = session_state["template_info"]["name"]
            filename = f"{template_name}_{timestamp}_{unique_id}.docx"

            # 保存生成的文档
            output_path = self.generated_docs_dir / filename
            doc.save(output_path)

            # 清理会话状态
            del self.session_states[session_id]

            LOG.info(f"文书生成成功: {filename}")

            return {
                "success": True,
                "message": f"🎉 文书生成成功！\n\n📄 **{template_name}** 已生成完成。\n\n您可以点击下方的下载按钮获取生成的Word文档。",
                "filename": filename,
                "file_path": str(output_path)
            }

        except Exception as e:
            LOG.error(f"文书生成失败: {str(e)}")
            return {
                "success": False,
                "message": f"抱歉，文书生成失败：{str(e)}",
                "filename": None,
                "file_path": None
            }

    def _create_document_by_template_id(self, template_id: str, fields: Dict[str, str]) -> Document:
        """根据模板ID创建专业法律文书"""
        LOG.info(f"开始创建文档，模板ID: {template_id}")

        # 根据模板ID调用相应的文档创建方法
        if template_id == "house_lease":
            return self._create_house_lease_document(fields)
        elif template_id == "traffic_accident":
            return self._create_traffic_accident_document(fields)
        elif template_id == "sales_contract":
            return self._create_sales_contract_document(fields)
        elif template_id == "divorce_agreement":
            return self._create_divorce_document(fields)
        elif template_id == "loan_dispute":
            return self._create_loan_dispute_document(fields)
        elif template_id == "labor_arbitration":
            return self._create_labor_arbitration_document(fields)
        else:
            # 创建基础文档
            return self._create_fallback_document(None, fields)

    def _fill_template_with_user_info(self, doc: Document, fields: Dict[str, str]):
        """在模板文档中填入用户信息"""
        # 创建更全面的替换映射，包括各种可能的占位符格式
        replacements = {
            # 带方括号的占位符
            "[姓名]": fields.get("当事人姓名", "___"),
            "[性别]": fields.get("性别", "___"),
            "[年龄]": fields.get("年龄", "___"),
            "[联系电话]": fields.get("联系电话", "___"),
            "[地址]": fields.get("地址", "___"),
            "[当事人姓名]": fields.get("当事人姓名", "___"),

            # 带下划线的占位符
            "___姓名___": fields.get("当事人姓名", "___"),
            "___性别___": fields.get("性别", "___"),
            "___年龄___": fields.get("年龄", "___"),
            "___联系电话___": fields.get("联系电话", "___"),
            "___地址___": fields.get("地址", "___"),

            # 常见的模板占位符
            "姓名：___": f"姓名：{fields.get('当事人姓名', '___')}",
            "性别：___": f"性别：{fields.get('性别', '___')}",
            "年龄：___": f"年龄：{fields.get('年龄', '___')}",
            "联系电话：___": f"联系电话：{fields.get('联系电话', '___')}",
            "地址：___": f"地址：{fields.get('地址', '___')}",

            # 特殊格式
            "XXX": fields.get("当事人姓名", "___"),  # 常见的姓名占位符
            "XX": fields.get("性别", "___") if len(fields.get("性别", "")) <= 2 else "___",  # 性别占位符
        }

        LOG.info(f"开始填充模板，用户信息: {fields}")

        # 替换段落中的内容
        for paragraph in doc.paragraphs:
            for placeholder, value in replacements.items():
                if placeholder in paragraph.text:
                    paragraph.text = paragraph.text.replace(placeholder, value)
                    LOG.info(f"在段落中替换 '{placeholder}' -> '{value}'")

        # 替换表格中的内容
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for placeholder, value in replacements.items():
                        if placeholder in cell.text:
                            cell.text = cell.text.replace(placeholder, value)
                            LOG.info(f"在表格中替换 '{placeholder}' -> '{value}'")




    def _create_house_lease_document(self, fields: Dict[str, str]) -> Document:
        """创建房屋租赁纠纷起诉状 - 基于templates/Complaint for House Lease.doc的结构"""
        doc = Document()

        # 标题
        title = doc.add_heading('民事起诉状', 0)
        title.alignment = 1  # 居中

        doc.add_paragraph()

        # 当事人信息 - 填入用户提供的信息
        p1 = doc.add_paragraph()
        p1.add_run('原告：').bold = True
        p1.add_run(f'{fields.get("当事人姓名", "张三")}，性别{fields.get("性别", "男")}，{fields.get("年龄", "35")}岁，住址{fields.get("地址", "北京市朝阳区某某街道123号")}，联系电话{fields.get("联系电话", "13800138000")}。')

        p2 = doc.add_paragraph()
        p2.add_run('被告：').bold = True
        p2.add_run('李四，性别女，28岁，住址北京市海淀区某某小区456号，联系电话13900139000。')

        doc.add_paragraph()

        # 诉讼请求
        req_title = doc.add_paragraph()
        req_title.add_run('诉讼请求：').bold = True

        doc.add_paragraph('1. 判令被告向原告支付拖欠的房屋租金12000元；')
        doc.add_paragraph('2. 判令被告向原告支付违约金3000元；')
        doc.add_paragraph('3. 判令被告立即搬离租赁房屋；')
        doc.add_paragraph('4. 本案诉讼费由被告承担。')

        doc.add_paragraph()

        # 事实与理由
        fact_title = doc.add_paragraph()
        fact_title.add_run('事实与理由：').bold = True

        doc.add_paragraph('原告与被告于2023年1月1日签订《房屋租赁合同》，约定被告租赁原告位于北京市朝阳区某某路888号的房屋，租赁期限为一年，月租金为3000元，每月1日前支付。')
        doc.add_paragraph('合同签订后，被告按约入住。但自2023年9月起，被告无正当理由拖欠租金，截至起诉之日，共拖欠租金12000元。经原告多次催要，被告仍拒不支付。')
        doc.add_paragraph('根据《中华人民共和国民法典》第七百二十一条规定，承租人应当按照约定的期限支付租金。被告的行为已构成违约，给原告造成了经济损失。')
        doc.add_paragraph('根据合同第八条约定，被告逾期支付租金超过15日的，原告有权解除合同并要求被告承担违约责任。')

        doc.add_paragraph()
        doc.add_paragraph('综上所述，被告的行为严重违反了合同约定，请求人民法院依法支持原告的诉讼请求。')

        doc.add_paragraph()
        doc.add_paragraph('此致')
        doc.add_paragraph('北京市朝阳区人民法院')

        doc.add_paragraph()
        doc.add_paragraph(f'起诉人：{fields.get("当事人姓名", "张三")}')
        doc.add_paragraph('2024年1月15日')

        # 添加附件清单
        doc.add_paragraph()
        attach_title = doc.add_paragraph()
        attach_title.add_run('附件：').bold = True
        doc.add_paragraph('1. 房屋租赁合同复印件1份')
        doc.add_paragraph('2. 催款通知书复印件1份')
        doc.add_paragraph('3. 其他相关证据材料')

        return doc

    def _create_traffic_accident_document(self, fields: Dict[str, str]) -> Document:
        """创建交通事故损害赔偿起诉状"""
        doc = Document()

        title = doc.add_heading('民事起诉状', 0)
        title.alignment = 1

        doc.add_paragraph()

        p1 = doc.add_paragraph()
        p1.add_run('原告：').bold = True
        p1.add_run(f'{fields.get("当事人姓名", "___")}，性别{fields.get("性别", "___")}，{fields.get("年龄", "___")}岁，住址{fields.get("地址", "___")}，联系电话{fields.get("联系电话", "___")}。')

        p2 = doc.add_paragraph()
        p2.add_run('被告：').bold = True
        p2.add_run('___，性别___，___岁，住址___，联系电话___。')

        doc.add_paragraph()

        req_title = doc.add_paragraph()
        req_title.add_run('诉讼请求：').bold = True

        doc.add_paragraph('1. 判令被告赔偿原告医疗费___元；')
        doc.add_paragraph('2. 判令被告赔偿原告误工费___元；')
        doc.add_paragraph('3. 判令被告赔偿原告交通费___元；')
        doc.add_paragraph('4. 判令被告赔偿原告精神损害抚慰金___元；')
        doc.add_paragraph('5. 本案诉讼费由被告承担。')

        doc.add_paragraph()

        fact_title = doc.add_paragraph()
        fact_title.add_run('事实与理由：').bold = True

        doc.add_paragraph('___年___月___日___时许，被告驾驶___号牌车辆沿___行驶至___路段时，与原告发生交通事故。')
        doc.add_paragraph('经交警部门认定，被告承担此次事故的主要责任，原告承担次要责任。')
        doc.add_paragraph('事故造成原告受伤，经医院诊断为___，产生医疗费___元，误工费___元等损失。')

        doc.add_paragraph()
        doc.add_paragraph('综上所述，请求人民法院依法支持原告的诉讼请求。')

        doc.add_paragraph()
        doc.add_paragraph('此致')
        doc.add_paragraph('___人民法院')

        doc.add_paragraph()
        doc.add_paragraph(f'起诉人：{fields.get("当事人姓名", "___")}')
        doc.add_paragraph('___年___月___日')

        return doc

    def _create_sales_contract_document(self, fields: Dict[str, str]) -> Document:
        """创建买卖合同纠纷起诉状"""
        doc = Document()

        title = doc.add_heading('民事起诉状', 0)
        title.alignment = 1

        doc.add_paragraph()

        p1 = doc.add_paragraph()
        p1.add_run('原告：').bold = True
        p1.add_run(f'{fields.get("当事人姓名", "___")}，性别{fields.get("性别", "___")}，{fields.get("年龄", "___")}岁，住址{fields.get("地址", "___")}，联系电话{fields.get("联系电话", "___")}。')

        p2 = doc.add_paragraph()
        p2.add_run('被告：').bold = True
        p2.add_run('___，性别___，___岁，住址___，联系电话___。')

        doc.add_paragraph()

        req_title = doc.add_paragraph()
        req_title.add_run('诉讼请求：').bold = True

        doc.add_paragraph('1. 判令被告履行买卖合同，交付货物；')
        doc.add_paragraph('2. 判令被告支付违约金___元；')
        doc.add_paragraph('3. 本案诉讼费由被告承担。')

        doc.add_paragraph()

        fact_title = doc.add_paragraph()
        fact_title.add_run('事实与理由：').bold = True

        doc.add_paragraph('___年___月___日，原告与被告签订买卖合同，约定被告向原告出售___，总价款___元。')
        doc.add_paragraph('合同签订后，原告按约支付了货款，但被告未按约定时间交付货物，构成违约。')
        doc.add_paragraph('经原告多次催促，被告仍未履行合同义务，给原告造成了损失。')

        doc.add_paragraph()
        doc.add_paragraph('综上所述，请求人民法院依法支持原告的诉讼请求。')

        doc.add_paragraph()
        doc.add_paragraph('此致')
        doc.add_paragraph('___人民法院')

        doc.add_paragraph()
        doc.add_paragraph(f'起诉人：{fields.get("当事人姓名", "___")}')
        doc.add_paragraph('___年___月___日')

        return doc

    def _create_divorce_document(self, fields: Dict[str, str]) -> Document:
        """创建离婚协议书 - 基于templates/Divorce agreement.doc的结构"""
        doc = Document()

        title = doc.add_heading('离婚协议书', 0)
        title.alignment = 1

        doc.add_paragraph()

        # 根据性别确定男女方信息 - 填入用户提供的信息
        if fields.get("性别") == "男":
            male_info = f'{fields.get("当事人姓名", "王五")}，{fields.get("年龄", "32")}岁，身份证号：110101199200101234，住址：{fields.get("地址", "北京市朝阳区某某街道123号")}，联系电话：{fields.get("联系电话", "13800138000")}。'
            female_info = '赵六，30岁，身份证号：110101199400201234，住址：北京市海淀区某某小区456号，联系电话：13900139000。'
        else:
            male_info = '王五，32岁，身份证号：110101199200101234，住址：北京市朝阳区某某街道789号，联系电话：13700137000。'
            female_info = f'{fields.get("当事人姓名", "赵六")}，{fields.get("年龄", "30")}岁，身份证号：110101199400201234，住址：{fields.get("地址", "北京市海淀区某某小区456号")}，联系电话：{fields.get("联系电话", "13900139000")}。'

        p1 = doc.add_paragraph()
        p1.add_run('男方：').bold = True
        p1.add_run(male_info)

        p2 = doc.add_paragraph()
        p2.add_run('女方：').bold = True
        p2.add_run(female_info)

        doc.add_paragraph()
        doc.add_paragraph('男女双方于2020年5月20日在北京市朝阳区民政局登记结婚，婚姻登记证号：京朝民结字第2020052000001号。现因双方性格不合，感情确已破裂，无法共同生活，经双方协商一致，自愿离婚，并就有关事项达成如下协议：')

        doc.add_paragraph()
        p3 = doc.add_paragraph()
        p3.add_run('一、离婚意思表示').bold = True
        doc.add_paragraph('男女双方自愿协议离婚。')

        doc.add_paragraph()
        p4 = doc.add_paragraph()
        p4.add_run('二、子女抚养').bold = True
        doc.add_paragraph('双方无子女。')

        doc.add_paragraph()
        p5 = doc.add_paragraph()
        p5.add_run('三、财产分割').bold = True
        doc.add_paragraph('1. 双方确认，婚姻关系存续期间无共同财产；')
        doc.add_paragraph('2. 各自名下的财产归各自所有；')
        doc.add_paragraph('3. 双方各自名下的债权债务由各自享有和承担。')

        doc.add_paragraph()
        p6 = doc.add_paragraph()
        p6.add_run('四、债务处理').bold = True
        doc.add_paragraph('双方确认在婚姻关系存续期间没有发生任何共同债务，任何一方如对外负有债务的，由负债方自行承担。')

        doc.add_paragraph()
        p7 = doc.add_paragraph()
        p7.add_run('五、其他约定').bold = True
        doc.add_paragraph('1. 双方保证切实履行本协议，如有隐瞒、欺骗，责任自负；')
        doc.add_paragraph('2. 本协议经双方签字后生效；')
        doc.add_paragraph('3. 本协议一式三份，双方各执一份，婚姻登记机关存档一份。')

        doc.add_paragraph()
        doc.add_paragraph()
        if fields.get("性别") == "男":
            doc.add_paragraph(f'男方签名：{fields.get("当事人姓名", "王五")}                    女方签名：赵六')
        else:
            doc.add_paragraph(f'男方签名：王五                    女方签名：{fields.get("当事人姓名", "赵六")}')
        doc.add_paragraph('签名日期：2024年1月15日                    签名日期：2024年1月15日')

        return doc

    def _create_loan_dispute_document(self, fields: Dict[str, str]) -> Document:
        """创建借款纠纷起诉状"""
        doc = Document()

        title = doc.add_heading('民事起诉状', 0)
        title.alignment = 1

        doc.add_paragraph()

        p1 = doc.add_paragraph()
        p1.add_run('原告：').bold = True
        p1.add_run(f'{fields.get("当事人姓名", "___")}，性别{fields.get("性别", "___")}，{fields.get("年龄", "___")}岁，住址{fields.get("地址", "___")}，联系电话{fields.get("联系电话", "___")}。')

        p2 = doc.add_paragraph()
        p2.add_run('被告：').bold = True
        p2.add_run('___，性别___，___岁，住址___，联系电话___。')

        doc.add_paragraph()

        req_title = doc.add_paragraph()
        req_title.add_run('诉讼请求：').bold = True

        doc.add_paragraph('1. 判令被告偿还借款本金___元；')
        doc.add_paragraph('2. 判令被告支付利息___元；')
        doc.add_paragraph('3. 本案诉讼费由被告承担。')

        doc.add_paragraph()

        fact_title = doc.add_paragraph()
        fact_title.add_run('事实与理由：').bold = True

        doc.add_paragraph('___年___月___日，被告因资金周转困难向原告借款___元，约定借款期限为___，利率为___。')
        doc.add_paragraph('借款到期后，经原告多次催要，被告仍未归还借款，已构成违约。')
        doc.add_paragraph('根据法律规定，被告应当承担还款责任并支付相应利息。')

        doc.add_paragraph()
        doc.add_paragraph('综上所述，请求人民法院依法支持原告的诉讼请求。')

        doc.add_paragraph()
        doc.add_paragraph('此致')
        doc.add_paragraph('___人民法院')

        doc.add_paragraph()
        doc.add_paragraph(f'起诉人：{fields.get("当事人姓名", "___")}')
        doc.add_paragraph('___年___月___日')

        return doc

    def _create_labor_arbitration_document(self, fields: Dict[str, str]) -> Document:
        """创建劳动仲裁申请书 - 基于templates/Labor Arbitration Registration form.doc的结构"""
        doc = Document()

        title = doc.add_heading('劳动争议仲裁申请书', 0)
        title.alignment = 1

        doc.add_paragraph()

        # 申请人信息 - 填入用户提供的信息
        p1 = doc.add_paragraph()
        p1.add_run('申请人：').bold = True
        p1.add_run(f'{fields.get("当事人姓名", "孙七")}，性别{fields.get("性别", "男")}，{fields.get("年龄", "28")}岁，身份证号：110101199600101234，住址：{fields.get("地址", "北京市朝阳区某某街道123号")}，联系电话：{fields.get("联系电话", "13800138000")}。')

        p2 = doc.add_paragraph()
        p2.add_run('被申请人：').bold = True
        p2.add_run('北京某某科技有限公司，住所地：北京市海淀区中关村大街1号，法定代表人：李某某，联系电话：010-88888888。')

        doc.add_paragraph()

        # 仲裁请求
        req_title = doc.add_paragraph()
        req_title.add_run('仲裁请求：').bold = True

        doc.add_paragraph('1. 裁决被申请人支付申请人2023年10月至12月工资15000元；')
        doc.add_paragraph('2. 裁决被申请人支付申请人经济补偿金5000元；')
        doc.add_paragraph('3. 裁决被申请人支付申请人加班费3000元；')
        doc.add_paragraph('4. 仲裁费用由被申请人承担。')

        doc.add_paragraph()

        # 事实与理由
        fact_title = doc.add_paragraph()
        fact_title.add_run('事实与理由：').bold = True

        doc.add_paragraph('申请人于2022年3月1日入职被申请人处工作，担任软件开发工程师职务，双方签订了为期两年的劳动合同，约定月工资5000元。')
        doc.add_paragraph('工作期间，申请人认真履行工作职责，经常加班完成工作任务。')
        doc.add_paragraph('2023年12月31日，被申请人以"公司业务调整"为由单方面解除与申请人的劳动合同，但未提前30日通知，也未支付代通知金。')
        doc.add_paragraph('被申请人解除劳动合同后，拖欠申请人2023年10月至12月工资共计15000元，未支付经济补偿金5000元，未支付加班费3000元。')
        doc.add_paragraph('根据《中华人民共和国劳动合同法》第四十七条、第八十七条等相关规定，被申请人应当支付拖欠工资、经济补偿金和加班费。')

        doc.add_paragraph()
        doc.add_paragraph('综上所述，被申请人的行为严重违反了劳动法律法规，请求仲裁委员会依法支持申请人的仲裁请求。')

        doc.add_paragraph()
        doc.add_paragraph('此致')
        doc.add_paragraph('北京市朝阳区劳动争议仲裁委员会')

        doc.add_paragraph()
        doc.add_paragraph(f'申请人：{fields.get("当事人姓名", "孙七")}')
        doc.add_paragraph('2024年1月15日')

        # 添加附件清单
        doc.add_paragraph()
        attach_title = doc.add_paragraph()
        attach_title.add_run('附件：').bold = True
        doc.add_paragraph('1. 劳动合同复印件1份')
        doc.add_paragraph('2. 工资条复印件3份')
        doc.add_paragraph('3. 解除劳动合同通知书复印件1份')
        doc.add_paragraph('4. 其他相关证据材料')

        return doc

    def _create_fallback_document(self, template_path, fields: Dict[str, str]) -> Document:
        """创建基础文档结构"""
        LOG.warning("创建基础文档")

        # 创建一个基础的Word文档
        doc = Document()

        # 添加标题
        title = doc.add_heading("法律文书", 0)
        title.alignment = 1  # 居中对齐

        doc.add_paragraph()

        # 添加基本信息段落
        info_paragraph = doc.add_paragraph()
        info_paragraph.add_run('当事人信息：').bold = True

        doc.add_paragraph(f'姓名：{fields.get("当事人姓名", "___")}')
        doc.add_paragraph(f'性别：{fields.get("性别", "___")}')
        doc.add_paragraph(f'年龄：{fields.get("年龄", "___")}岁')
        doc.add_paragraph(f'联系电话：{fields.get("联系电话", "___")}')
        doc.add_paragraph(f'地址：{fields.get("地址", "___")}')

        doc.add_paragraph()
        doc.add_paragraph('（此文档基于基础模板生成，请根据实际需要进行修改）')

        return doc

    def get_generated_document(self, filename: str) -> Optional[str]:
        """获取生成的文档路径"""
        file_path = self.generated_docs_dir / filename
        if file_path.exists():
            return str(file_path)
        return None

    def cleanup_old_documents(self, days: int = 7):
        """清理旧的生成文档"""
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)

            for file_path in self.generated_docs_dir.glob("*.docx"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    LOG.info(f"已清理旧文档: {file_path.name}")
        except Exception as e:
            LOG.error(f"清理旧文档失败: {str(e)}")
