"""
基于内容的律师推荐引擎
"""

import re
import math
import jieba
import jieba.analyse
from typing import List, Dict, Any, Tuple, Set
from utils.logger import LOG

class ContentBasedRecommendationEngine:
    """基于内容的推荐引擎"""
    
    def __init__(self):
        """初始化推荐引擎"""
        # 加载停用词
        self.stopwords = self._load_stopwords()
        
        # 法律领域关键词权重
        self.legal_keywords_weights = {
            # 法律领域
            "合同": 2.0, "纠纷": 1.5, "诉讼": 1.5, "仲裁": 1.5, "辩护": 2.0,
            "知识产权": 2.0, "专利": 1.8, "商标": 1.8, "著作权": 1.8,
            "婚姻": 2.0, "离婚": 1.8, "抚养": 1.5, "继承": 1.5,
            "劳动": 2.0, "工伤": 1.8, "赔偿": 1.5, "补偿": 1.5,
            "公司": 2.0, "股权": 1.8, "投资": 1.5, "并购": 1.8,
            "房产": 2.0, "租赁": 1.5, "买卖": 1.5, "物业": 1.5,
            "刑事": 2.0, "犯罪": 1.8, "辩护": 1.8, "取保": 1.5,
            "行政": 2.0, "许可": 1.5, "处罚": 1.5, "复议": 1.5,
            "债权": 1.8, "债务": 1.8, "借贷": 1.5, "担保": 1.5,
            "损害": 1.5, "侵权": 1.8, "赔偿": 1.5, "责任": 1.5,
            
            # 地域关键词
            "北京": 1.2, "上海": 1.2, "广州": 1.2, "深圳": 1.2,
            "杭州": 1.0, "南京": 1.0, "成都": 1.0, "武汉": 1.0,
            "天津": 1.0, "重庆": 1.0, "西安": 1.0, "苏州": 1.0,
            
            # 律师特性
            "专业": 0.8, "经验": 0.8, "资深": 0.8, "知名": 0.8,
            "团队": 0.7, "事务所": 0.7, "咨询": 0.6, "顾问": 0.6,
        }
        
        # 初始化jieba分词
        for keyword in self.legal_keywords_weights:
            jieba.suggest_freq(keyword, True)
        
        LOG.info("[RecommendationEngine] 基于内容的推荐引擎初始化完成")
    
    def _load_stopwords(self) -> Set[str]:
        """加载停用词"""
        # 常用停用词
        stopwords = {
            "的", "了", "和", "是", "在", "我", "有", "个", "要", "这", "就", "会", "对", "到",
            "可以", "为", "那", "你", "我们", "他", "她", "它", "们", "这个", "那个", "什么", "怎么",
            "如何", "为什么", "哪些", "谁", "哪里", "什么样", "多少", "如果", "因为", "所以", "但是",
            "而且", "不过", "虽然", "然而", "不", "没有", "一个", "一些", "一下", "现在", "已经",
            "正在", "将要", "曾经", "一直", "立即", "马上", "赶快", "立刻", "顺便", "另外", "此外",
            "除此之外", "不仅", "不只", "而是", "并且", "以及", "还有", "还是", "或者", "或是",
            "需要", "想要", "希望", "想", "觉得", "认为", "以为", "相信", "知道", "了解", "明白",
            "请问", "请", "麻烦", "帮忙", "帮助", "谢谢", "感谢", "不客气", "没关系", "不用谢"
        }
        return stopwords
    
    def recommend(self, user_requirements: str, lawyer_teams: List[Dict[str, Any]], top_n: int = 5) -> List[Dict[str, Any]]:
        """
        基于内容的律师推荐
        
        参数:
            user_requirements: 用户需求描述
            lawyer_teams: 律师团队列表
            top_n: 返回推荐结果数量
            
        返回:
            推荐的律师团队列表，按相似度排序
        """
        try:
            # 1. 提取用户需求特征向量
            user_vector = self._extract_features(user_requirements)
            
            # 2. 计算每个律师团队的特征向量和相似度
            team_similarities = []
            
            for team in lawyer_teams:
                # 提取律师团队特征
                team_vector = self._extract_team_features(team)
                
                # 计算相似度
                similarity = self._calculate_similarity(user_vector, team_vector)
                
                # 应用额外的匹配规则
                similarity = self._apply_matching_rules(similarity, user_requirements, team)
                
                team_similarities.append({
                    "team": team,
                    "similarity": similarity
                })
            
            # 3. 根据相似度排序
            team_similarities.sort(key=lambda x: x["similarity"], reverse=True)
            
            # 4. 返回前N个结果
            recommended_teams = [item["team"] for item in team_similarities[:top_n]]
            
            # 5. 记录推荐结果
            self._log_recommendation(user_requirements, recommended_teams)
            
            return recommended_teams
            
        except Exception as e:
            LOG.error(f"[RecommendationEngine] 推荐失败: {str(e)}")
            # 如果推荐失败，返回原始列表的前N个
            return lawyer_teams[:min(top_n, len(lawyer_teams))]
    
    def _extract_features(self, text: str) -> Dict[str, float]:
        """
        从文本中提取特征向量
        
        参数:
            text: 输入文本
            
        返回:
            特征向量 {词: 权重}
        """
        # 清理文本
        text = self._clean_text(text)
        
        # 使用jieba提取关键词和权重
        keywords = jieba.analyse.extract_tags(text, topK=20, withWeight=True)
        
        # 转换为字典并应用领域权重
        features = {}
        for word, weight in keywords:
            if word not in self.stopwords:
                # 应用领域权重
                domain_weight = self.legal_keywords_weights.get(word, 1.0)
                features[word] = weight * domain_weight
        
        # 提取地域信息
        locations = self._extract_locations(text)
        for location in locations:
            if location in features:
                features[location] *= 1.5  # 增强地域特征权重
            else:
                features[location] = self.legal_keywords_weights.get(location, 1.0)
        
        # 提取法律专业领域
        legal_areas = self._extract_legal_areas(text)
        for area in legal_areas:
            if area in features:
                features[area] *= 2.0  # 增强专业领域权重
            else:
                features[area] = self.legal_keywords_weights.get(area, 1.5)
        
        return features
    
    def _extract_team_features(self, team: Dict[str, Any]) -> Dict[str, float]:
        """
        提取律师团队特征
        
        参数:
            team: 律师团队信息
            
        返回:
            特征向量 {词: 权重}
        """
        features = {}
        
        # 添加专业领域特征
        specialties = team.get("specialties", [])
        for specialty in specialties:
            # 对专业领域进行分词
            for word in jieba.cut(specialty):
                if word not in self.stopwords:
                    # 专业领域词权重较高
                    domain_weight = self.legal_keywords_weights.get(word, 1.0)
                    features[word] = 2.0 * domain_weight
        
        # 添加地域特征
        location = team.get("location", "")
        if location:
            features[location] = 1.5
        
        # 添加描述特征
        description = team.get("description", "")
        if description:
            # 提取描述中的关键词
            desc_keywords = jieba.analyse.extract_tags(description, topK=10, withWeight=True)
            for word, weight in desc_keywords:
                if word not in self.stopwords:
                    domain_weight = self.legal_keywords_weights.get(word, 1.0)
                    if word in features:
                        features[word] += weight * domain_weight * 0.8  # 描述权重稍低
                    else:
                        features[word] = weight * domain_weight * 0.8
        
        # 添加案例特征
        notable_cases = team.get("notable_cases", [])
        for case in notable_cases:
            case_keywords = jieba.analyse.extract_tags(case, topK=5, withWeight=True)
            for word, weight in case_keywords:
                if word not in self.stopwords:
                    domain_weight = self.legal_keywords_weights.get(word, 1.0)
                    if word in features:
                        features[word] += weight * domain_weight * 1.2  # 案例权重较高
                    else:
                        features[word] = weight * domain_weight * 1.2
        
        return features
    
    def _calculate_similarity(self, vector1: Dict[str, float], vector2: Dict[str, float]) -> float:
        """
        计算两个特征向量的余弦相似度
        
        参数:
            vector1: 特征向量1
            vector2: 特征向量2
            
        返回:
            相似度分数 (0-1)
        """
        # 获取所有特征词
        all_features = set(vector1.keys()) | set(vector2.keys())
        
        # 如果没有特征词，返回0
        if not all_features:
            return 0.0
        
        # 计算点积
        dot_product = sum(vector1.get(feature, 0) * vector2.get(feature, 0) for feature in all_features)
        
        # 计算向量模长
        magnitude1 = math.sqrt(sum(vector1.get(feature, 0) ** 2 for feature in all_features))
        magnitude2 = math.sqrt(sum(vector2.get(feature, 0) ** 2 for feature in all_features))
        
        # 避免除以零
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        # 计算余弦相似度
        similarity = dot_product / (magnitude1 * magnitude2)
        
        return similarity
    
    def _apply_matching_rules(self, similarity: float, user_requirements: str, team: Dict[str, Any]) -> float:
        """
        应用额外的匹配规则调整相似度
        
        参数:
            similarity: 初始相似度
            user_requirements: 用户需求
            team: 律师团队信息
            
        返回:
            调整后的相似度
        """
        adjusted_similarity = similarity
        
        # 1. 评分加权
        rating = team.get("rating", 0)
        if rating > 4.5:
            adjusted_similarity *= 1.15  # 高评分加权
        elif rating > 4.0:
            adjusted_similarity *= 1.1
        
        # 2. 经验年限加权
        experience_years = team.get("experience_years", 0)
        if experience_years > 20:
            adjusted_similarity *= 1.1  # 丰富经验加权
        elif experience_years > 10:
            adjusted_similarity *= 1.05
        
        # 3. 团队规模加权
        team_size = team.get("team_size", 0)
        if team_size > 100:
            adjusted_similarity *= 1.05  # 大型团队加权
        
        # 4. 地域匹配加权
        locations = self._extract_locations(user_requirements)
        team_location = team.get("location", "")
        if locations and any(location in team_location for location in locations):
            adjusted_similarity *= 1.2  # 地域匹配加权
        
        # 5. 专业领域精确匹配加权
        legal_areas = self._extract_legal_areas(user_requirements)
        team_specialties = team.get("specialties", [])
        if legal_areas and any(area in specialty for area in legal_areas for specialty in team_specialties):
            adjusted_similarity *= 1.25  # 专业领域精确匹配加权
        
        return adjusted_similarity
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 去除特殊字符
        text = re.sub(r'[^\w\s]', ' ', text)
        # 去除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def _extract_locations(self, text: str) -> List[str]:
        """提取地域信息"""
        locations = []
        location_keywords = [
            "北京", "上海", "广州", "深圳", "杭州", "南京", "成都", "武汉",
            "天津", "重庆", "西安", "苏州", "长沙", "郑州", "青岛", "宁波",
            "东莞", "无锡", "厦门", "福州", "大连", "合肥", "济南", "哈尔滨"
        ]
        
        for location in location_keywords:
            if location in text:
                locations.append(location)
        
        return locations
    
    def _extract_legal_areas(self, text: str) -> List[str]:
        """提取法律专业领域"""
        legal_areas = []
        legal_area_keywords = [
            "合同", "纠纷", "诉讼", "仲裁", "知识产权", "专利", "商标", "著作权",
            "婚姻", "离婚", "抚养", "继承", "劳动", "工伤", "赔偿", "公司",
            "股权", "投资", "并购", "房产", "租赁", "买卖", "刑事", "辩护",
            "行政", "许可", "处罚", "债权", "债务", "借贷", "担保", "侵权"
        ]
        
        for area in legal_area_keywords:
            if area in text:
                legal_areas.append(area)
        
        return legal_areas
    
    def _log_recommendation(self, user_requirements: str, recommended_teams: List[Dict[str, Any]]) -> None:
        """记录推荐结果"""
        try:
            LOG.info(f"[RecommendationEngine] 用户需求: {user_requirements}")
            LOG.info(f"[RecommendationEngine] 推荐结果: {[team.get('name', '') for team in recommended_teams]}")
        except Exception as e:
            LOG.error(f"[RecommendationEngine] 记录推荐结果失败: {str(e)}")
