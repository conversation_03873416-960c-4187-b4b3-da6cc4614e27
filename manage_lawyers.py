#!/usr/bin/env python3
"""
律师团队数据管理工具
"""

import json
import os
from typing import Dict, List, Any

class LawyerTeamManager:
    """律师团队数据管理器"""
    
    def __init__(self):
        self.data_file = os.path.join(os.path.dirname(__file__), 'data', 'lawyer_teams.json')
        self.teams = self.load_teams()
    
    def load_teams(self) -> List[Dict[str, Any]]:
        """加载律师团队数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"数据文件不存在: {self.data_file}")
            return []
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return []
    
    def save_teams(self):
        """保存律师团队数据"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.teams, f, ensure_ascii=False, indent=4)
            print(f"✅ 数据已保存到 {self.data_file}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def list_teams(self):
        """列出所有律师团队"""
        print(f"\n📋 当前共有 {len(self.teams)} 个律师团队:")
        print("="*80)
        for team in self.teams:
            print(f"ID: {team['id']}")
            print(f"名称: {team['name']}")
            print(f"地区: {team['location']}")
            print(f"专业领域: {', '.join(team['specialties'])}")
            print(f"评分: {team['rating']}/5.0")
            print(f"团队规模: {team['team_size']}人")
            print("-" * 40)
    
    def add_team(self, team_data: Dict[str, Any]):
        """添加新的律师团队"""
        # 自动分配ID
        max_id = max([team['id'] for team in self.teams], default=0)
        team_data['id'] = max_id + 1
        
        self.teams.append(team_data)
        print(f"✅ 已添加律师团队: {team_data['name']}")
    
    def remove_team(self, team_id: int):
        """删除律师团队"""
        original_count = len(self.teams)
        self.teams = [team for team in self.teams if team['id'] != team_id]
        
        if len(self.teams) < original_count:
            print(f"✅ 已删除ID为 {team_id} 的律师团队")
        else:
            print(f"❌ 未找到ID为 {team_id} 的律师团队")
    
    def update_team(self, team_id: int, updates: Dict[str, Any]):
        """更新律师团队信息"""
        for team in self.teams:
            if team['id'] == team_id:
                team.update(updates)
                print(f"✅ 已更新ID为 {team_id} 的律师团队")
                return
        print(f"❌ 未找到ID为 {team_id} 的律师团队")
    
    def search_teams(self, keyword: str):
        """搜索律师团队"""
        results = []
        keyword_lower = keyword.lower()
        
        for team in self.teams:
            if (keyword_lower in team['name'].lower() or
                keyword_lower in team['location'].lower() or
                any(keyword_lower in specialty.lower() for specialty in team['specialties'])):
                results.append(team)
        
        print(f"\n🔍 搜索 '{keyword}' 的结果 ({len(results)} 个):")
        for team in results:
            print(f"- {team['name']} ({team['location']}) - {', '.join(team['specialties'])}")
    
    def get_statistics(self):
        """获取统计信息"""
        if not self.teams:
            print("📊 暂无数据")
            return
        
        # 地区分布
        locations = {}
        for team in self.teams:
            location = team['location']
            locations[location] = locations.get(location, 0) + 1
        
        # 专业领域分布
        specialties = {}
        for team in self.teams:
            for specialty in team['specialties']:
                specialties[specialty] = specialties.get(specialty, 0) + 1
        
        # 评分统计
        ratings = [team['rating'] for team in self.teams]
        avg_rating = sum(ratings) / len(ratings)
        
        print("\n📊 统计信息:")
        print(f"总团队数: {len(self.teams)}")
        print(f"平均评分: {avg_rating:.2f}")
        print(f"最高评分: {max(ratings)}")
        print(f"最低评分: {min(ratings)}")
        
        print("\n📍 地区分布:")
        for location, count in sorted(locations.items()):
            print(f"  {location}: {count} 个团队")
        
        print("\n🎯 专业领域分布:")
        for specialty, count in sorted(specialties.items(), key=lambda x: x[1], reverse=True):
            print(f"  {specialty}: {count} 个团队")

def main():
    """主函数"""
    manager = LawyerTeamManager()
    
    while True:
        print("\n" + "="*50)
        print("🏛️  律师团队数据管理工具")
        print("="*50)
        print("1. 列出所有团队")
        print("2. 搜索团队")
        print("3. 查看统计信息")
        print("4. 添加团队")
        print("5. 删除团队")
        print("6. 保存数据")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-6): ").strip()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            manager.list_teams()
        elif choice == '2':
            keyword = input("请输入搜索关键词: ").strip()
            if keyword:
                manager.search_teams(keyword)
        elif choice == '3':
            manager.get_statistics()
        elif choice == '4':
            print("添加新团队功能需要在代码中实现具体的输入逻辑")
        elif choice == '5':
            try:
                team_id = int(input("请输入要删除的团队ID: ").strip())
                manager.remove_team(team_id)
            except ValueError:
                print("❌ 请输入有效的数字ID")
        elif choice == '6':
            manager.save_teams()
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
