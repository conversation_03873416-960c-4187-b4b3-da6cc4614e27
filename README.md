# LCA 法律咨询助手

## 📋 项目概述

LCA (LegalConsultationAssistant) 是一款基于大模型的专业法律咨询助手系统，提供全面的法律问题解答和场景化法律咨询服务。用户可以选择不同的法律场景进行咨询，或直接与法律助手进行问答对话，学习法律知识，获得专业的法律指导。

## 🚀 快速开始

### 1. 环境准备
```bash
# 进入项目目录
cd LegalConsultationAssistant

# 安装依赖
pip install -r requirements_html.txt
```

### 2. 配置API密钥
创建 `.env` 文件并配置：
```env
OPENAI_API_KEY=your_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
```

或者在Web界面的设置中直接配置。

### 3. 启动应用
```bash
python main_html.py
```

### 4. 访问界面
- 🌐 Web界面: http://localhost:8000
- 🔧 API服务: http://localhost:5000

## ✨ 功能特性

### ⚖️ 法律场景咨询
专门针对三种常见法律场景提供专业咨询：

#### 💔 婚姻纠纷
- 离婚程序和条件
- 财产分割和债务承担
- 子女抚养权和抚养费
- 家庭暴力和保护令

#### 📄 合同纠纷
- 合同效力认定
- 违约责任和损害赔偿
- 合同解除和终止
- 各类合同争议解决

#### 🏗️ 工伤赔偿
- 工伤认定标准和程序
- 伤残等级鉴定
- 赔偿项目和标准计算
- 维权程序指导

### 💬 法律知识问答
- 开放式法律问题解答
- 专业法律条文解释
- 实时法律咨询服务
- 案例分析和建议

### 🔍 法律案例检索
- 基于12309中国检察网的案例检索
- 支持罪名类型和关键词搜索
- 提供详细案件信息和判决文书链接
- 智能案例分析和法律建议

### 📚 法律书籍学习
系统学习三部核心法律：

#### 📜 宪法学习
- 国家根本大法原理
- 国家机构组织和职权
- 公民基本权利和义务
- 宪法实施和监督

#### ⚖️ 刑法学习
- 犯罪构成要件
- 刑罚种类和适用
- 具体犯罪条文
- 刑事责任原则

#### 📋 民法典学习
- 总则编：民事主体、权利、法律行为
- 物权编：所有权、用益物权、担保物权
- 合同编：合同订立、履行、违约责任
- 人格权编：生命权、身体权、健康权
- 婚姻家庭编：结婚、离婚、收养
- 继承编：法定继承、遗嘱继承
- 侵权责任编：侵权行为、损害赔偿

## 🎨 界面特色

### 专业法律设计
- 深蓝色和金色的专业配色方案
- 符合法律行业严肃性的界面设计
- 清晰的信息层次和布局

### 对话体验优化
- 类似QQ/微信的对话界面
- 用户和助手头像显示
- 实时消息时间戳
- 流畅的动画效果

## 🔧 技术架构

### 前端技术
- HTML5 + CSS3 + JavaScript
- 响应式设计，支持移动端
- 现代化UI组件
- 实时API交互

### 后端技术
- Flask Web框架
- RESTful API设计
- 智能体核心保持不变
- 会话管理和历史记录

### 智能体系统
- 场景专用智能体（婚姻、合同、工伤）
- 通用法律问答智能体
- 法律教育智能体
- 统一的会话管理

## 📁 项目结构

```
LegalConsultationAssistant/
├── web/                    # 前端文件
│   ├── index.html         # 主页面
│   ├── css/               # 样式文件
│   └── js/                # JavaScript文件
├── api/                   # 后端API
│   └── app.py            # Flask应用
├── agents/                # 智能体模块
│   ├── scenario_agent.py  # 场景智能体
│   ├── conversation_agent.py # 对话智能体
│   ├── vocab_agent.py     # 学习智能体
│   └── case_search_agent.py # 案例检索智能体
├── prompts/               # 提示词文件
├── content/               # 内容资源
│   ├── intro/            # 初始对话
│   └── page/             # 页面介绍
├── images/                # 图片资源
├── utils/                 # 工具模块
├── test_case_search.py    # 案例检索测试脚本
└── main_html.py          # 主启动文件
```

## 🛠️ 开发和扩展

### 添加新的法律场景
1. 在 `prompts/` 目录添加新的提示词文件
2. 在 `content/intro/` 添加初始对话文件
3. 在 `content/page/` 添加场景介绍文件
4. 更新前端场景选项和API路由

### 扩展案例检索功能
1. 在 `agents/case_search_agent.py` 中添加新的罪名类型
2. 更新 `_mock_search_cases` 方法中的案例数据库
3. 在 `_extract_search_params` 方法中添加新的罪名识别规则
4. 更新前端罪名标签和提示信息

### 自定义界面样式
- 修改 `web/css/main.css` 调整主题色彩
- 修改 `web/css/components.css` 调整组件样式
- 更新 `web/index.html` 修改页面结构

## 🔍 故障排除

### 常见问题

**Q: 无法启动服务**
- 检查Python环境和依赖安装
- 确认端口5000和8000未被占用
- 查看控制台错误信息

**Q: API调用失败**
- 检查API Key配置是否正确
- 确认网络连接正常
- 查看API服务日志

**Q: 界面显示异常**
- 清除浏览器缓存
- 检查CSS和JS文件是否正确加载
- 确认Web服务器正常运行

### 日志查看
应用日志输出到控制台和 `logs/app.log` 文件

## 🎯 使用建议

1. **首次使用**：建议先体验场景咨询功能，了解系统能力
2. **学习模式**：按照宪法→民法典→刑法的顺序进行系统学习
3. **问答咨询**：遇到具体法律问题时使用法律问答功能
4. **重要提醒**：本系统提供的是法律信息参考，重大法律问题请咨询专业律师

---

**感谢使用 LCA 法律咨询助手！**
