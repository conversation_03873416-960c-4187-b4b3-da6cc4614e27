"""
文档模板创建工具
用于创建带有占位符的Word文档模板
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from pathlib import Path
import os


def create_house_lease_complaint_template():
    """创建房屋租赁纠纷起诉状模板"""
    doc = Document()
    
    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # 标题
    title = doc.add_heading('民事起诉状', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 原告信息
    doc.add_paragraph()
    p1 = doc.add_paragraph()
    p1.add_run('原告：').bold = True
    p1.add_run('{原告姓名}，身份证号：{原告身份证号}，联系电话：{原告联系电话}，住址：{原告地址}。')
    
    # 被告信息
    p2 = doc.add_paragraph()
    p2.add_run('被告：').bold = True
    p2.add_run('{被告姓名}，身份证号：{被告身份证号}，联系电话：{被告联系电话}，住址：{被告地址}。')
    
    # 诉讼请求
    doc.add_paragraph()
    req_title = doc.add_paragraph()
    req_title.add_run('诉讼请求：').bold = True
    
    doc.add_paragraph('{诉讼请求}')
    
    # 事实与理由
    doc.add_paragraph()
    fact_title = doc.add_paragraph()
    fact_title.add_run('事实与理由：').bold = True
    
    doc.add_paragraph('原告与被告于{租赁期限}签订房屋租赁合同，约定租赁房屋位于{租赁房屋地址}，租金为{租金金额}。')
    doc.add_paragraph('{纠纷事实}')
    
    # 证据
    doc.add_paragraph()
    evidence_title = doc.add_paragraph()
    evidence_title.add_run('证据：').bold = True
    doc.add_paragraph('1. 房屋租赁合同；')
    doc.add_paragraph('2. 相关付款凭证；')
    doc.add_paragraph('3. 其他相关证据材料。')
    
    # 结尾
    doc.add_paragraph()
    doc.add_paragraph('综上所述，请求人民法院依法支持原告的诉讼请求。')
    
    doc.add_paragraph()
    doc.add_paragraph('此致')
    doc.add_paragraph('人民法院')
    
    doc.add_paragraph()
    doc.add_paragraph('原告：{原告姓名}')
    doc.add_paragraph('日期：    年    月    日')
    
    return doc


def create_traffic_accident_complaint_template():
    """创建交通事故损害赔偿起诉状模板"""
    doc = Document()
    
    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # 标题
    title = doc.add_heading('民事起诉状', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 原告信息
    doc.add_paragraph()
    p1 = doc.add_paragraph()
    p1.add_run('原告：').bold = True
    p1.add_run('{原告姓名}，身份证号：{原告身份证号}，联系电话：{原告联系电话}，住址：{原告地址}。')
    
    # 被告信息
    p2 = doc.add_paragraph()
    p2.add_run('被告：').bold = True
    p2.add_run('{被告姓名}，身份证号：{被告身份证号}，联系电话：{被告联系电话}，住址：{被告地址}。')
    
    # 诉讼请求
    doc.add_paragraph()
    req_title = doc.add_paragraph()
    req_title.add_run('诉讼请求：').bold = True
    
    doc.add_paragraph('{诉讼请求}')
    
    # 事实与理由
    doc.add_paragraph()
    fact_title = doc.add_paragraph()
    fact_title.add_run('事实与理由：').bold = True
    
    doc.add_paragraph('于{事故时间}，在{事故地点}发生交通事故。')
    doc.add_paragraph('{事故经过}')
    doc.add_paragraph('因此次事故，原告遭受以下损失：{损失情况}')
    
    # 证据
    doc.add_paragraph()
    evidence_title = doc.add_paragraph()
    evidence_title.add_run('证据：').bold = True
    doc.add_paragraph('1. 交通事故认定书；')
    doc.add_paragraph('2. 医疗费用票据；')
    doc.add_paragraph('3. 其他相关证据材料。')
    
    # 结尾
    doc.add_paragraph()
    doc.add_paragraph('综上所述，请求人民法院依法支持原告的诉讼请求。')
    
    doc.add_paragraph()
    doc.add_paragraph('此致')
    doc.add_paragraph('人民法院')
    
    doc.add_paragraph()
    doc.add_paragraph('原告：{原告姓名}')
    doc.add_paragraph('日期：    年    月    日')
    
    return doc


def create_divorce_agreement_template():
    """创建离婚协议书模板"""
    doc = Document()
    
    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
    
    # 标题
    title = doc.add_heading('离婚协议书', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # 当事人信息
    doc.add_paragraph()
    p1 = doc.add_paragraph()
    p1.add_run('男方：').bold = True
    p1.add_run('{男方姓名}，身份证号：{男方身份证号}，联系电话：{男方联系电话}，住址：{男方地址}。')
    
    p2 = doc.add_paragraph()
    p2.add_run('女方：').bold = True
    p2.add_run('{女方姓名}，身份证号：{女方身份证号}，联系电话：{女方联系电话}，住址：{女方地址}。')
    
    # 协议内容
    doc.add_paragraph()
    doc.add_paragraph('男女双方于{结婚时间}登记结婚，现因感情不和，自愿离婚，经双方协商一致，对有关事项达成如下协议：')
    
    doc.add_paragraph()
    p3 = doc.add_paragraph()
    p3.add_run('一、子女抚养：').bold = True
    doc.add_paragraph('{子女情况}')
    
    doc.add_paragraph()
    p4 = doc.add_paragraph()
    p4.add_run('二、财产分割：').bold = True
    doc.add_paragraph('{财产分割}')
    
    doc.add_paragraph()
    p5 = doc.add_paragraph()
    p5.add_run('三、债务处理：').bold = True
    doc.add_paragraph('{债务处理}')
    
    doc.add_paragraph()
    p6 = doc.add_paragraph()
    p6.add_run('四、其他约定：').bold = True
    doc.add_paragraph('{其他约定}')
    
    # 签名
    doc.add_paragraph()
    doc.add_paragraph('本协议一式三份，双方各执一份，婚姻登记机关存档一份。')
    
    doc.add_paragraph()
    doc.add_paragraph('男方签名：{男方姓名}        女方签名：{女方姓名}')
    doc.add_paragraph('日期：    年    月    日        日期：    年    月    日')
    
    return doc


def create_all_templates():
    """创建所有模板文件"""
    templates_dir = Path(__file__).parent.parent / "templates"
    templates_dir.mkdir(exist_ok=True)
    
    # 创建房屋租赁纠纷起诉状模板
    house_lease_doc = create_house_lease_complaint_template()
    house_lease_doc.save(templates_dir / "Complaint for House Lease.docx")
    print("已创建：房屋租赁纠纷起诉状模板")
    
    # 创建交通事故损害赔偿起诉状模板
    traffic_accident_doc = create_traffic_accident_complaint_template()
    traffic_accident_doc.save(templates_dir / "Complaint for Traffic Accident.docx")
    print("已创建：交通事故损害赔偿起诉状模板")
    
    # 创建离婚协议书模板
    divorce_doc = create_divorce_agreement_template()
    divorce_doc.save(templates_dir / "Divorce agreement.docx")
    print("已创建：离婚协议书模板")


if __name__ == "__main__":
    create_all_templates()
    print("所有模板创建完成！")
