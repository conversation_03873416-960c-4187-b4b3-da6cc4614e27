import json
from .agent_base import AgentBase
from utils.logger import LOG
from langchain_core.messages import HumanMessage
import os
from langchain_community.chat_models import ChatTongyi

class CaseSearchAgent(AgentBase):
    """
    案例检索智能体，直接通过千问API检索中国真实判决案例
    """
    def __init__(self, session_id=None):
        super().__init__(
            name="case_search",
            prompt_file="prompts/case_search_prompt.txt",
            session_id=session_id
        )
        # 初始化千问API模型
        from dotenv import load_dotenv, find_dotenv
        _ = load_dotenv(find_dotenv())
        self.llm = ChatTongyi(
            model="qwen-turbo",
            dashscope_api_key=os.getenv("Qwen_API_KEY"),
            max_tokens=2048,
            temperature=0.7,
            streaming=False,
            top_p=0.8,
            timeout=30
        )

    def search_cases(self, crime_type, keywords=None, limit=5):
        """
        通过千问API检索案例
        """
        try:
            LOG.info(f"千问API检索案例: {crime_type}, 关键词: {keywords}")
            prompt = self._build_case_prompt(crime_type, keywords, limit)
            response = self.llm.invoke([HumanMessage(content=prompt)])
            # 解析大模型返回的JSON结构
            cases = self._parse_llm_response(response.content)
            LOG.info(f"千问API返回案例数: {len(cases)}")
            return cases
        except Exception as e:
            LOG.error(f"千问API案例检索失败: {str(e)}")
            return []

    def _build_case_prompt(self, crime_type, keywords, limit):
        """
        构造千问API案例检索prompt
        """
        search_desc = crime_type
        if keywords:
            search_desc += f" {keywords}"
        prompt = (
            f"请列举{limit}个与“{search_desc}”相关的中国真实判决案例，请在https://wenshu.court.gov.cn/上查找，切忌胡编乱造。要求每个案例包含如下字段：\n"
            f"title（案件标题）、case_number（案件编号）、court（审理法院）、crime_type（罪名）、sentence（判决结果）、judgment_date（判决日期）、case_details（案件详情）、url（判决文书网址）。\n"
            f"请以JSON数组格式返回，字段名用英文。不要有多余解释。"
        )
        return prompt

    def _parse_llm_response(self, content):
        """
        尝试解析大模型返回的JSON结构
        """
        try:
            # 提取JSON数组
            start = content.find('[')
            end = content.rfind(']')
            if start != -1 and end != -1:
                json_str = content[start:end+1]
                cases = json.loads(json_str)
                # 只保留需要的字段
                result = []
                for case in cases:
                    result.append({
                        'title': case.get('title', ''),
                        'case_number': case.get('case_number', ''),
                        'court': case.get('court', ''),
                        'crime_type': case.get('crime_type', ''),
                        'sentence': case.get('sentence', ''),
                        'judgment_date': case.get('judgment_date', ''),
                        'case_details': case.get('case_details', ''),
                        'url': case.get('url', '')
                    })
                return result
            else:
                LOG.error("千问返回内容中未找到JSON数组")
                return []
        except Exception as e:
            LOG.error(f"解析千问API返回内容失败: {str(e)}\n原始内容: {content}")
            return []

    def format_case_response(self, cases, crime_type, keywords=None):
        if not cases:
            return f"抱歉，未找到与'{crime_type}'相关的案例。请尝试使用其他罪名类型或关键词。"

        response = f"📋 案例检索结果<br><br>"
        search_condition = crime_type
        if keywords:
            search_condition += f" + {keywords}"
        response += f"搜索条件: {search_condition}<br><br>"
        response += f"找到 **{len(cases)}** 个相关案例：<br>"

        for i, case in enumerate(cases, 1):
            response += f"{i}. **{case['title']}**<br>"
            response += f"审理法院: {case['court']}<br>"
            response += f"罪名: {case['crime_type']}<br>"
            response += f"判决结果: {case['sentence']}<br>"
            response += f"判决日期: {case['judgment_date']}<br>"
            response += f"案件详情: {case['case_details']}<br>"
            response += f"判决文书: {case['url']}<br>"
            response += "<br>"

        response += f"💡 **提示**: 以上案例仅供参考，具体案件处理请咨询专业律师。<br>"
        response += f"🔍 **继续搜索**: 您可以提供更多关键词或罪名类型，我将为您检索更多相关案例。"
        return response

    def chat_with_history(self, user_input, session_id=None):
        if session_id is None:
            session_id = self.session_id
        if self._is_case_search_request(user_input):
            return self._handle_case_search(user_input, session_id)
        else:
            return super().chat_with_history(user_input, session_id)

    def _is_case_search_request(self, user_input):
        search_keywords = ['案例', '判例', '判决', '案件', '检索', '查找', '搜索']
        return any(keyword in user_input for keyword in search_keywords)

    def _handle_case_search(self, user_input, session_id):
        try:
            crime_type, keywords = self._extract_search_params(user_input)
            if not crime_type:
                return "请提供具体的罪名类型，例如：盗窃、诈骗、故意伤害等。我可以为您检索相关案例。"
            cases = self.search_cases(crime_type, keywords)
            response = self.format_case_response(cases, crime_type, keywords)
            return response
        except Exception as e:
            LOG.error(f"案例检索处理失败: {str(e)}")
            return f"案例检索过程中出现错误: {str(e)}"

    def _extract_search_params(self, user_input):
        crime_types = [
            "盗窃", "诈骗", "故意伤害", "故意杀人", "抢劫", "强奸", 
            "合同诈骗", "职务侵占", "挪用公款", "贪污", "受贿", 
            "非法持有毒品", "贩卖毒品", "交通肇事", "危险驾驶",
            "寻衅滋事", "聚众斗殴", "敲诈勒索", "绑架", "拐卖妇女儿童"
        ]
        crime_type = None
        for crime in crime_types:
            if crime in user_input:
                crime_type = crime
                break
        keywords = user_input
        if crime_type:
            keywords = user_input.replace(crime_type, '').strip()
        return crime_type, keywords if keywords else None 