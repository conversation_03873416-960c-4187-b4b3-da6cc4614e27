<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by , GNU Texinfo 7.1 -->
  <head>
    <meta charset="utf-8">
    <title>
      ffprobe Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      ffprobe Documentation
      </h1>


<div class="top-level-extent" id="SEC_Top">

<div class="element-contents" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="toc-numbered-mark">
  <li><a id="toc-Synopsis" href="#Synopsis">1 Synopsis</a></li>
  <li><a id="toc-Description" href="#Description">2 Description</a></li>
  <li><a id="toc-Options" href="#Options">3 Options</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-Stream-specifiers-1" href="#Stream-specifiers-1">3.1 Stream specifiers</a></li>
    <li><a id="toc-Generic-options" href="#Generic-options">3.2 Generic options</a></li>
    <li><a id="toc-AVOptions" href="#AVOptions">3.3 AVOptions</a></li>
    <li><a id="toc-Main-options" href="#Main-options">3.4 Main options</a></li>
  </ul></li>
  <li><a id="toc-Writers" href="#Writers">4 Writers</a>
  <ul class="toc-numbered-mark">
    <li><a id="toc-default" href="#default">4.1 default</a></li>
    <li><a id="toc-compact_002c-csv" href="#compact_002c-csv">4.2 compact, csv</a></li>
    <li><a id="toc-flat" href="#flat">4.3 flat</a></li>
    <li><a id="toc-ini" href="#ini">4.4 ini</a></li>
    <li><a id="toc-json" href="#json">4.5 json</a></li>
    <li><a id="toc-xml" href="#xml">4.6 xml</a></li>
  </ul></li>
  <li><a id="toc-Timecode" href="#Timecode">5 Timecode</a></li>
  <li><a id="toc-See-Also" href="#See-Also">6 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">7 Authors</a></li>
</ul>
</div>
</div>

<ul class="mini-toc">
<li><a href="#Synopsis" accesskey="1">Synopsis</a></li>
<li><a href="#Description" accesskey="2">Description</a></li>
<li><a href="#Options" accesskey="3">Options</a></li>
<li><a href="#Writers" accesskey="4">Writers</a></li>
<li><a href="#Timecode" accesskey="5">Timecode</a></li>
<li><a href="#See-Also" accesskey="6">See Also</a></li>
<li><a href="#Authors" accesskey="7">Authors</a></li>
</ul>
<div class="chapter-level-extent" id="Synopsis">
<h2 class="chapter"><span>1 Synopsis<a class="copiable-link" href="#Synopsis"> &para;</a></span></h2>

<p>ffprobe [<var class="var">options</var>] <samp class="file">input_url</samp>
</p>
</div>
<div class="chapter-level-extent" id="Description">
<h2 class="chapter"><span>2 Description<a class="copiable-link" href="#Description"> &para;</a></span></h2>

<p>ffprobe gathers information from multimedia streams and prints it in
human- and machine-readable fashion.
</p>
<p>For example it can be used to check the format of the container used
by a multimedia stream and the format and type of each media stream
contained in it.
</p>
<p>If a url is specified in input, ffprobe will try to open and
probe the url content. If the url cannot be opened or recognized as
a multimedia file, a positive exit code is returned.
</p>
<p>If no output is specified as output with <samp class="option">o</samp> ffprobe will write
to stdout.
</p>
<p>ffprobe may be employed both as a standalone application or in
combination with a textual filter, which may perform more
sophisticated processing, e.g. statistical processing or plotting.
</p>
<p>Options are used to list some of the formats supported by ffprobe or
for specifying which information to display, and for setting how
ffprobe will show it.
</p>
<p>ffprobe output is designed to be easily parsable by a textual filter,
and consists of one or more sections of a form defined by the selected
writer, which is specified by the <samp class="option">output_format</samp> option.
</p>
<p>Sections may contain other nested sections, and are identified by a
name (which may be shared by other sections), and an unique
name. See the output of <samp class="option">sections</samp>.
</p>
<p>Metadata tags stored in the container or in the streams are recognized
and printed in the corresponding &quot;FORMAT&quot;, &quot;STREAM&quot;, &quot;STREAM_GROUP_STREAM&quot;
or &quot;PROGRAM_STREAM&quot; section.
</p>

</div>
<div class="chapter-level-extent" id="Options">
<h2 class="chapter"><span>3 Options<a class="copiable-link" href="#Options"> &para;</a></span></h2>

<p>All the numerical options, if not specified otherwise, accept a string
representing a number as input, which may be followed by one of the SI
unit prefixes, for example: &rsquo;K&rsquo;, &rsquo;M&rsquo;, or &rsquo;G&rsquo;.
</p>
<p>If &rsquo;i&rsquo; is appended to the SI unit prefix, the complete prefix will be
interpreted as a unit prefix for binary multiples, which are based on
powers of 1024 instead of powers of 1000. Appending &rsquo;B&rsquo; to the SI unit
prefix multiplies the value by 8. This allows using, for example:
&rsquo;KB&rsquo;, &rsquo;MiB&rsquo;, &rsquo;G&rsquo; and &rsquo;B&rsquo; as number suffixes.
</p>
<p>Options which do not take arguments are boolean options, and set the
corresponding value to true. They can be set to false by prefixing
the option name with &quot;no&quot;. For example using &quot;-nofoo&quot;
will set the boolean option with name &quot;foo&quot; to false.
</p>
<p>Options that take arguments support a special syntax where the argument given on
the command line is interpreted as a path to the file from which the actual
argument value is loaded. To use this feature, add a forward slash &rsquo;/&rsquo;
immediately before the option name (after the leading dash). E.g.
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i INPUT -/filter:v filter.script OUTPUT
</pre></div>
<p>will load a filtergraph description from the file named <samp class="file">filter.script</samp>.
</p>
<a class="anchor" id="Stream-specifiers"></a><ul class="mini-toc">
<li><a href="#Stream-specifiers-1" accesskey="1">Stream specifiers</a></li>
<li><a href="#Generic-options" accesskey="2">Generic options</a></li>
<li><a href="#AVOptions" accesskey="3">AVOptions</a></li>
<li><a href="#Main-options" accesskey="4">Main options</a></li>
</ul>
<div class="section-level-extent" id="Stream-specifiers-1">
<h3 class="section"><span>3.1 Stream specifiers<a class="copiable-link" href="#Stream-specifiers-1"> &para;</a></span></h3>
<p>Some options are applied per-stream, e.g. bitrate or codec. Stream specifiers
are used to precisely specify which stream(s) a given option belongs to.
</p>
<p>A stream specifier is a string generally appended to the option name and
separated from it by a colon. E.g. <code class="code">-codec:a:1 ac3</code> contains the
<code class="code">a:1</code> stream specifier, which matches the second audio stream. Therefore, it
would select the ac3 codec for the second audio stream.
</p>
<p>A stream specifier can match several streams, so that the option is applied to all
of them. E.g. the stream specifier in <code class="code">-b:a 128k</code> matches all audio
streams.
</p>
<p>An empty stream specifier matches all streams. For example, <code class="code">-codec copy</code>
or <code class="code">-codec: copy</code> would copy all the streams without reencoding.
</p>
<p>Possible forms of stream specifiers are:
</p><dl class="table">
<dt><samp class="option"><var class="var">stream_index</var></samp></dt>
<dd><p>Matches the stream with this index. E.g. <code class="code">-threads:1 4</code> would set the
thread count for the second stream to 4. If <var class="var">stream_index</var> is used as an
additional stream specifier (see below), then it selects stream number
<var class="var">stream_index</var> from the matching streams. Stream numbering is based on the
order of the streams as detected by libavformat except when a stream group
specifier or program ID is also specified. In this case it is based on the
ordering of the streams in the group or program.
</p></dd>
<dt><samp class="option"><var class="var">stream_type</var>[:<var class="var">additional_stream_specifier</var>]</samp></dt>
<dd><p><var class="var">stream_type</var> is one of following: &rsquo;v&rsquo; or &rsquo;V&rsquo; for video, &rsquo;a&rsquo; for audio, &rsquo;s&rsquo;
for subtitle, &rsquo;d&rsquo; for data, and &rsquo;t&rsquo; for attachments. &rsquo;v&rsquo; matches all video
streams, &rsquo;V&rsquo; only matches video streams which are not attached pictures, video
thumbnails or cover arts. If <var class="var">additional_stream_specifier</var> is used, then
it matches streams which both have this type and match the
<var class="var">additional_stream_specifier</var>. Otherwise, it matches all streams of the
specified type.
</p></dd>
<dt><samp class="option">g:<var class="var">group_specifier</var>[:<var class="var">additional_stream_specifier</var>]</samp></dt>
<dd><p>Matches streams which are in the group with the specifier <var class="var">group_specifier</var>.
if <var class="var">additional_stream_specifier</var> is used, then it matches streams which both
are part of the group and match the <var class="var">additional_stream_specifier</var>.
<var class="var">group_specifier</var> may be one of the following:
</p><dl class="table">
<dt><samp class="option"><var class="var">group_index</var></samp></dt>
<dd><p>Match the stream with this group index.
</p></dd>
<dt><samp class="option">#<var class="var">group_id</var> or i:<var class="var">group_id</var></samp></dt>
<dd><p>Match the stream with this group id.
</p></dd>
</dl>
</dd>
<dt><samp class="option">p:<var class="var">program_id</var>[:<var class="var">additional_stream_specifier</var>]</samp></dt>
<dd><p>Matches streams which are in the program with the id <var class="var">program_id</var>. If
<var class="var">additional_stream_specifier</var> is used, then it matches streams which both
are part of the program and match the <var class="var">additional_stream_specifier</var>.
</p>
</dd>
<dt><samp class="option">#<var class="var">stream_id</var> or i:<var class="var">stream_id</var></samp></dt>
<dd><p>Match the stream by stream id (e.g. PID in MPEG-TS container).
</p></dd>
<dt><samp class="option">m:<var class="var">key</var>[:<var class="var">value</var>]</samp></dt>
<dd><p>Matches streams with the metadata tag <var class="var">key</var> having the specified value. If
<var class="var">value</var> is not given, matches streams that contain the given tag with any
value.
</p></dd>
<dt><samp class="option">u</samp></dt>
<dd><p>Matches streams with usable configuration, the codec must be defined and the
essential information such as video dimension or audio sample rate must be present.
</p>
<p>Note that in <code class="command">ffmpeg</code>, matching by metadata will only work properly for
input files.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="Generic-options">
<h3 class="section"><span>3.2 Generic options<a class="copiable-link" href="#Generic-options"> &para;</a></span></h3>

<p>These options are shared amongst the ff* tools.
</p>
<dl class="table">
<dt><samp class="option">-L</samp></dt>
<dd><p>Show license.
</p>
</dd>
<dt><samp class="option">-h, -?, -help, --help [<var class="var">arg</var>]</samp></dt>
<dd><p>Show help. An optional parameter may be specified to print help about a specific
item. If no argument is specified, only basic (non advanced) tool
options are shown.
</p>
<p>Possible values of <var class="var">arg</var> are:
</p><dl class="table">
<dt><samp class="option">long</samp></dt>
<dd><p>Print advanced tool options in addition to the basic tool options.
</p>
</dd>
<dt><samp class="option">full</samp></dt>
<dd><p>Print complete list of options, including shared and private options
for encoders, decoders, demuxers, muxers, filters, etc.
</p>
</dd>
<dt><samp class="option">decoder=<var class="var">decoder_name</var></samp></dt>
<dd><p>Print detailed information about the decoder named <var class="var">decoder_name</var>. Use the
<samp class="option">-decoders</samp> option to get a list of all decoders.
</p>
</dd>
<dt><samp class="option">encoder=<var class="var">encoder_name</var></samp></dt>
<dd><p>Print detailed information about the encoder named <var class="var">encoder_name</var>. Use the
<samp class="option">-encoders</samp> option to get a list of all encoders.
</p>
</dd>
<dt><samp class="option">demuxer=<var class="var">demuxer_name</var></samp></dt>
<dd><p>Print detailed information about the demuxer named <var class="var">demuxer_name</var>. Use the
<samp class="option">-formats</samp> option to get a list of all demuxers and muxers.
</p>
</dd>
<dt><samp class="option">muxer=<var class="var">muxer_name</var></samp></dt>
<dd><p>Print detailed information about the muxer named <var class="var">muxer_name</var>. Use the
<samp class="option">-formats</samp> option to get a list of all muxers and demuxers.
</p>
</dd>
<dt><samp class="option">filter=<var class="var">filter_name</var></samp></dt>
<dd><p>Print detailed information about the filter named <var class="var">filter_name</var>. Use the
<samp class="option">-filters</samp> option to get a list of all filters.
</p>
</dd>
<dt><samp class="option">bsf=<var class="var">bitstream_filter_name</var></samp></dt>
<dd><p>Print detailed information about the bitstream filter named <var class="var">bitstream_filter_name</var>.
Use the <samp class="option">-bsfs</samp> option to get a list of all bitstream filters.
</p>
</dd>
<dt><samp class="option">protocol=<var class="var">protocol_name</var></samp></dt>
<dd><p>Print detailed information about the protocol named <var class="var">protocol_name</var>.
Use the <samp class="option">-protocols</samp> option to get a list of all protocols.
</p></dd>
</dl>

</dd>
<dt><samp class="option">-version</samp></dt>
<dd><p>Show version.
</p>
</dd>
<dt><samp class="option">-buildconf</samp></dt>
<dd><p>Show the build configuration, one option per line.
</p>
</dd>
<dt><samp class="option">-formats</samp></dt>
<dd><p>Show available formats (including devices).
</p>
</dd>
<dt><samp class="option">-demuxers</samp></dt>
<dd><p>Show available demuxers.
</p>
</dd>
<dt><samp class="option">-muxers</samp></dt>
<dd><p>Show available muxers.
</p>
</dd>
<dt><samp class="option">-devices</samp></dt>
<dd><p>Show available devices.
</p>
</dd>
<dt><samp class="option">-codecs</samp></dt>
<dd><p>Show all codecs known to libavcodec.
</p>
<p>Note that the term &rsquo;codec&rsquo; is used throughout this documentation as a shortcut
for what is more correctly called a media bitstream format.
</p>
</dd>
<dt><samp class="option">-decoders</samp></dt>
<dd><p>Show available decoders.
</p>
</dd>
<dt><samp class="option">-encoders</samp></dt>
<dd><p>Show all available encoders.
</p>
</dd>
<dt><samp class="option">-bsfs</samp></dt>
<dd><p>Show available bitstream filters.
</p>
</dd>
<dt><samp class="option">-protocols</samp></dt>
<dd><p>Show available protocols.
</p>
</dd>
<dt><samp class="option">-filters</samp></dt>
<dd><p>Show available libavfilter filters.
</p>
</dd>
<dt><samp class="option">-pix_fmts</samp></dt>
<dd><p>Show available pixel formats.
</p>
</dd>
<dt><samp class="option">-sample_fmts</samp></dt>
<dd><p>Show available sample formats.
</p>
</dd>
<dt><samp class="option">-layouts</samp></dt>
<dd><p>Show channel names and standard channel layouts.
</p>
</dd>
<dt><samp class="option">-dispositions</samp></dt>
<dd><p>Show stream dispositions.
</p>
</dd>
<dt><samp class="option">-colors</samp></dt>
<dd><p>Show recognized color names.
</p>
</dd>
<dt><samp class="option">-sources <var class="var">device</var>[,<var class="var">opt1</var>=<var class="var">val1</var>[,<var class="var">opt2</var>=<var class="var">val2</var>]...]</samp></dt>
<dd><p>Show autodetected sources of the input device.
Some devices may provide system-dependent source names that cannot be autodetected.
The returned list cannot be assumed to be always complete.
</p><div class="example">
<pre class="example-preformatted">ffmpeg -sources pulse,server=192.168.0.4
</pre></div>

</dd>
<dt><samp class="option">-sinks <var class="var">device</var>[,<var class="var">opt1</var>=<var class="var">val1</var>[,<var class="var">opt2</var>=<var class="var">val2</var>]...]</samp></dt>
<dd><p>Show autodetected sinks of the output device.
Some devices may provide system-dependent sink names that cannot be autodetected.
The returned list cannot be assumed to be always complete.
</p><div class="example">
<pre class="example-preformatted">ffmpeg -sinks pulse,server=192.168.0.4
</pre></div>

</dd>
<dt><samp class="option">-loglevel [<var class="var">flags</var>+]<var class="var">loglevel</var> | -v [<var class="var">flags</var>+]<var class="var">loglevel</var></samp></dt>
<dd><p>Set logging level and flags used by the library.
</p>
<p>The optional <var class="var">flags</var> prefix can consist of the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">repeat</samp>&rsquo;</dt>
<dd><p>Indicates that repeated log output should not be compressed to the first line
and the &quot;Last message repeated n times&quot; line will be omitted.
</p></dd>
<dt>&lsquo;<samp class="samp">level</samp>&rsquo;</dt>
<dd><p>Indicates that log output should add a <code class="code">[level]</code> prefix to each message
line. This can be used as an alternative to log coloring, e.g. when dumping the
log to file.
</p></dd>
</dl>
<p>Flags can also be used alone by adding a &rsquo;+&rsquo;/&rsquo;-&rsquo; prefix to set/reset a single
flag without affecting other <var class="var">flags</var> or changing <var class="var">loglevel</var>. When
setting both <var class="var">flags</var> and <var class="var">loglevel</var>, a &rsquo;+&rsquo; separator is expected
between the last <var class="var">flags</var> value and before <var class="var">loglevel</var>.
</p>
<p><var class="var">loglevel</var> is a string or a number containing one of the following values:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">quiet, -8</samp>&rsquo;</dt>
<dd><p>Show nothing at all; be silent.
</p></dd>
<dt>&lsquo;<samp class="samp">panic, 0</samp>&rsquo;</dt>
<dd><p>Only show fatal errors which could lead the process to crash, such as
an assertion failure. This is not currently used for anything.
</p></dd>
<dt>&lsquo;<samp class="samp">fatal, 8</samp>&rsquo;</dt>
<dd><p>Only show fatal errors. These are errors after which the process absolutely
cannot continue.
</p></dd>
<dt>&lsquo;<samp class="samp">error, 16</samp>&rsquo;</dt>
<dd><p>Show all errors, including ones which can be recovered from.
</p></dd>
<dt>&lsquo;<samp class="samp">warning, 24</samp>&rsquo;</dt>
<dd><p>Show all warnings and errors. Any message related to possibly
incorrect or unexpected events will be shown.
</p></dd>
<dt>&lsquo;<samp class="samp">info, 32</samp>&rsquo;</dt>
<dd><p>Show informative messages during processing. This is in addition to
warnings and errors. This is the default value.
</p></dd>
<dt>&lsquo;<samp class="samp">verbose, 40</samp>&rsquo;</dt>
<dd><p>Same as <code class="code">info</code>, except more verbose.
</p></dd>
<dt>&lsquo;<samp class="samp">debug, 48</samp>&rsquo;</dt>
<dd><p>Show everything, including debugging information.
</p></dd>
<dt>&lsquo;<samp class="samp">trace, 56</samp>&rsquo;</dt>
</dl>

<p>For example to enable repeated log output, add the <code class="code">level</code> prefix, and set
<var class="var">loglevel</var> to <code class="code">verbose</code>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -loglevel repeat+level+verbose -i input output
</pre></div>
<p>Another example that enables repeated log output without affecting current
state of <code class="code">level</code> prefix flag or <var class="var">loglevel</var>:
</p><div class="example">
<pre class="example-preformatted">ffmpeg [...] -loglevel +repeat
</pre></div>

<p>By default the program logs to stderr. If coloring is supported by the
terminal, colors are used to mark errors and warnings. Log coloring
can be disabled setting the environment variable
<code class="env">AV_LOG_FORCE_NOCOLOR</code>, or can be forced setting
the environment variable <code class="env">AV_LOG_FORCE_COLOR</code>.
</p>
</dd>
<dt><samp class="option">-report</samp></dt>
<dd><p>Dump full command line and log output to a file named
<code class="code"><var class="var">program</var>-<var class="var">YYYYMMDD</var>-<var class="var">HHMMSS</var>.log</code> in the current
directory.
This file can be useful for bug reports.
It also implies <code class="code">-loglevel debug</code>.
</p>
<p>Setting the environment variable <code class="env">FFREPORT</code> to any value has the
same effect. If the value is a &rsquo;:&rsquo;-separated key=value sequence, these
options will affect the report; option values must be escaped if they
contain special characters or the options delimiter &rsquo;:&rsquo; (see the
&ldquo;Quoting and escaping&rdquo; section in the ffmpeg-utils manual).
</p>
<p>The following options are recognized:
</p><dl class="table">
<dt><samp class="option">file</samp></dt>
<dd><p>set the file name to use for the report; <code class="code">%p</code> is expanded to the name
of the program, <code class="code">%t</code> is expanded to a timestamp, <code class="code">%%</code> is expanded
to a plain <code class="code">%</code>
</p></dd>
<dt><samp class="option">level</samp></dt>
<dd><p>set the log verbosity level using a numerical value (see <code class="code">-loglevel</code>).
</p></dd>
</dl>

<p>For example, to output a report to a file named <samp class="file">ffreport.log</samp>
using a log level of <code class="code">32</code> (alias for log level <code class="code">info</code>):
</p>
<div class="example">
<pre class="example-preformatted">FFREPORT=file=ffreport.log:level=32 ffmpeg -i input output
</pre></div>

<p>Errors in parsing the environment variable are not fatal, and will not
appear in the report.
</p>
</dd>
<dt><samp class="option">-hide_banner</samp></dt>
<dd><p>Suppress printing banner.
</p>
<p>All FFmpeg tools will normally show a copyright notice, build options
and library versions. This option can be used to suppress printing
this information.
</p>
</dd>
<dt><samp class="option">-cpuflags flags (<em class="emph">global</em>)</samp></dt>
<dd><p>Allows setting and clearing cpu flags. This option is intended
for testing. Do not use it unless you know what you&rsquo;re doing.
</p><div class="example">
<pre class="example-preformatted">ffmpeg -cpuflags -sse+mmx ...
ffmpeg -cpuflags mmx ...
ffmpeg -cpuflags 0 ...
</pre></div>
<p>Possible flags for this option are:
</p><dl class="table">
<dt>&lsquo;<samp class="samp">x86</samp>&rsquo;</dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">mmx</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">mmxext</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">sse</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">sse2</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">sse2slow</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">sse3</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">sse3slow</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">ssse3</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">atom</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">sse4.1</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">sse4.2</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">avx</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">avx2</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">xop</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">fma3</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">fma4</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">3dnow</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">3dnowext</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bmi1</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">bmi2</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">cmov</samp>&rsquo;</dt>
</dl>
</dd>
<dt>&lsquo;<samp class="samp">ARM</samp>&rsquo;</dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">armv5te</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">armv6</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">armv6t2</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">vfp</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">vfpv3</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">neon</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">setend</samp>&rsquo;</dt>
</dl>
</dd>
<dt>&lsquo;<samp class="samp">AArch64</samp>&rsquo;</dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">armv8</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">vfp</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">neon</samp>&rsquo;</dt>
</dl>
</dd>
<dt>&lsquo;<samp class="samp">PowerPC</samp>&rsquo;</dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">altivec</samp>&rsquo;</dt>
</dl>
</dd>
<dt>&lsquo;<samp class="samp">Specific Processors</samp>&rsquo;</dt>
<dd><dl class="table">
<dt>&lsquo;<samp class="samp">pentium2</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">pentium3</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">pentium4</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">k6</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">k62</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">athlon</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">athlonxp</samp>&rsquo;</dt>
<dt>&lsquo;<samp class="samp">k8</samp>&rsquo;</dt>
</dl>
</dd>
</dl>

</dd>
<dt><samp class="option">-cpucount <var class="var">count</var> (<em class="emph">global</em>)</samp></dt>
<dd><p>Override detection of CPU count. This option is intended
for testing. Do not use it unless you know what you&rsquo;re doing.
</p><div class="example">
<pre class="example-preformatted">ffmpeg -cpucount 2
</pre></div>

</dd>
<dt><samp class="option">-max_alloc <var class="var">bytes</var></samp></dt>
<dd><p>Set the maximum size limit for allocating a block on the heap by ffmpeg&rsquo;s
family of malloc functions. Exercise <strong class="strong">extreme caution</strong> when using
this option. Don&rsquo;t use if you do not understand the full consequence of doing so.
Default is INT_MAX.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="AVOptions">
<h3 class="section"><span>3.3 AVOptions<a class="copiable-link" href="#AVOptions"> &para;</a></span></h3>

<p>These options are provided directly by the libavformat, libavdevice and
libavcodec libraries. To see the list of available AVOptions, use the
<samp class="option">-help</samp> option. They are separated into two categories:
</p><dl class="table">
<dt><samp class="option">generic</samp></dt>
<dd><p>These options can be set for any container, codec or device. Generic options
are listed under AVFormatContext options for containers/devices and under
AVCodecContext options for codecs.
</p></dd>
<dt><samp class="option">private</samp></dt>
<dd><p>These options are specific to the given container, device or codec. Private
options are listed under their corresponding containers/devices/codecs.
</p></dd>
</dl>

<p>For example to write an ID3v2.3 header instead of a default ID3v2.4 to
an MP3 file, use the <samp class="option">id3v2_version</samp> private option of the MP3
muxer:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i input.flac -id3v2_version 3 out.mp3
</pre></div>

<p>All codec AVOptions are per-stream, and thus a stream specifier
should be attached to them:
</p><div class="example">
<pre class="example-preformatted">ffmpeg -i multichannel.mxf -map 0:v:0 -map 0:a:0 -map 0:a:0 -c:a:0 ac3 -b:a:0 640k -ac:a:1 2 -c:a:1 aac -b:2 128k out.mp4
</pre></div>

<p>In the above example, a multichannel audio stream is mapped twice for output.
The first instance is encoded with codec ac3 and bitrate 640k.
The second instance is downmixed to 2 channels and encoded with codec aac. A bitrate of 128k is specified for it using
absolute index of the output stream.
</p>
<p>Note: the <samp class="option">-nooption</samp> syntax cannot be used for boolean
AVOptions, use <samp class="option">-option 0</samp>/<samp class="option">-option 1</samp>.
</p>
<p>Note: the old undocumented way of specifying per-stream AVOptions by
prepending v/a/s to the options name is now obsolete and will be
removed soon.
</p>
</div>
<div class="section-level-extent" id="Main-options">
<h3 class="section"><span>3.4 Main options<a class="copiable-link" href="#Main-options"> &para;</a></span></h3>

<dl class="table">
<dt><samp class="option">-f <var class="var">format</var></samp></dt>
<dd><p>Force format to use.
</p>
</dd>
<dt><samp class="option">-unit</samp></dt>
<dd><p>Show the unit of the displayed values.
</p>
</dd>
<dt><samp class="option">-prefix</samp></dt>
<dd><p>Use SI prefixes for the displayed values.
Unless the &quot;-byte_binary_prefix&quot; option is used all the prefixes
are decimal.
</p>
</dd>
<dt><samp class="option">-byte_binary_prefix</samp></dt>
<dd><p>Force the use of binary prefixes for byte values.
</p>
</dd>
<dt><samp class="option">-sexagesimal</samp></dt>
<dd><p>Use sexagesimal format HH:MM:SS.MICROSECONDS for time values.
</p>
</dd>
<dt><samp class="option">-pretty</samp></dt>
<dd><p>Prettify the format of the displayed values, it corresponds to the
options &quot;-unit -prefix -byte_binary_prefix -sexagesimal&quot;.
</p>
</dd>
<dt><samp class="option">-output_format, -of, -print_format <var class="var">writer_name</var>[=<var class="var">writer_options</var>]</samp></dt>
<dd><p>Set the output printing format.
</p>
<p><var class="var">writer_name</var> specifies the name of the writer, and
<var class="var">writer_options</var> specifies the options to be passed to the writer.
</p>
<p>For example for printing the output in JSON format, specify:
</p><div class="example">
<pre class="example-preformatted">-output_format json
</pre></div>

<p>For more details on the available output printing formats, see the
Writers section below.
</p>
</dd>
<dt><samp class="option">-sections</samp></dt>
<dd><p>Print sections structure and section information, and exit. The output
is not meant to be parsed by a machine.
</p>
</dd>
<dt><samp class="option">-select_streams <var class="var">stream_specifier</var></samp></dt>
<dd><p>Select only the streams specified by <var class="var">stream_specifier</var>. This
option affects only the options related to streams
(e.g. <code class="code">show_streams</code>, <code class="code">show_packets</code>, etc.).
</p>
<p>For example to show only audio streams, you can use the command:
</p><div class="example">
<pre class="example-preformatted">ffprobe -show_streams -select_streams a INPUT
</pre></div>

<p>To show only video packets belonging to the video stream with index 1:
</p><div class="example">
<pre class="example-preformatted">ffprobe -show_packets -select_streams v:1 INPUT
</pre></div>

</dd>
<dt><samp class="option">-show_data</samp></dt>
<dd><p>Show payload data, as a hexadecimal and ASCII dump. Coupled with
<samp class="option">-show_packets</samp>, it will dump the packets&rsquo; data. Coupled with
<samp class="option">-show_streams</samp>, it will dump the codec extradata.
</p>
<p>The dump is printed as the &quot;data&quot; field. It may contain newlines.
</p>
</dd>
<dt><samp class="option">-show_data_hash <var class="var">algorithm</var></samp></dt>
<dd><p>Show a hash of payload data, for packets with <samp class="option">-show_packets</samp> and for
codec extradata with <samp class="option">-show_streams</samp>.
</p>
</dd>
<dt><samp class="option">-show_error</samp></dt>
<dd><p>Show information about the error found when trying to probe the input.
</p>
<p>The error information is printed within a section with name &quot;ERROR&quot;.
</p>
</dd>
<dt><samp class="option">-show_format</samp></dt>
<dd><p>Show information about the container format of the input multimedia
stream.
</p>
<p>All the container format information is printed within a section with
name &quot;FORMAT&quot;.
</p>
</dd>
<dt><samp class="option">-show_format_entry <var class="var">name</var></samp></dt>
<dd><p>Like <samp class="option">-show_format</samp>, but only prints the specified entry of the
container format information, rather than all. This option may be given more
than once, then all specified entries will be shown.
</p>
<p>This option is deprecated, use <code class="code">show_entries</code> instead.
</p>
</dd>
<dt><samp class="option">-show_entries <var class="var">section_entries</var></samp></dt>
<dd><p>Set list of entries to show.
</p>
<p>Entries are specified according to the following
syntax. <var class="var">section_entries</var> contains a list of section entries
separated by <code class="code">:</code>. Each section entry is composed by a section
name (or unique name), optionally followed by a list of entries local
to that section, separated by <code class="code">,</code>.
</p>
<p>If section name is specified but is followed by no <code class="code">=</code>, all
entries are printed to output, together with all the contained
sections. Otherwise only the entries specified in the local section
entries list are printed. In particular, if <code class="code">=</code> is specified but
the list of local entries is empty, then no entries will be shown for
that section.
</p>
<p>Note that the order of specification of the local section entries is
not honored in the output, and the usual display order will be
retained.
</p>
<p>The formal syntax is given by:
</p><div class="example">
<pre class="example-preformatted"><var class="var">LOCAL_SECTION_ENTRIES</var> ::= <var class="var">SECTION_ENTRY_NAME</var>[,<var class="var">LOCAL_SECTION_ENTRIES</var>]
<var class="var">SECTION_ENTRY</var>         ::= <var class="var">SECTION_NAME</var>[=[<var class="var">LOCAL_SECTION_ENTRIES</var>]]
<var class="var">SECTION_ENTRIES</var>       ::= <var class="var">SECTION_ENTRY</var>[:<var class="var">SECTION_ENTRIES</var>]
</pre></div>

<p>For example, to show only the index and type of each stream, and the PTS
time, duration time, and stream index of the packets, you can specify
the argument:
</p><div class="example">
<pre class="example-preformatted">packet=pts_time,duration_time,stream_index : stream=index,codec_type
</pre></div>

<p>To show all the entries in the section &quot;format&quot;, but only the codec
type in the section &quot;stream&quot;, specify the argument:
</p><div class="example">
<pre class="example-preformatted">format : stream=codec_type
</pre></div>

<p>To show all the tags in the stream and format sections:
</p><div class="example">
<pre class="example-preformatted">stream_tags : format_tags
</pre></div>

<p>To show only the <code class="code">title</code> tag (if available) in the stream
sections:
</p><div class="example">
<pre class="example-preformatted">stream_tags=title
</pre></div>

</dd>
<dt><samp class="option">-show_packets</samp></dt>
<dd><p>Show information about each packet contained in the input multimedia
stream.
</p>
<p>The information for each single packet is printed within a dedicated
section with name &quot;PACKET&quot;.
</p>
</dd>
<dt><samp class="option">-show_frames</samp></dt>
<dd><p>Show information about each frame and subtitle contained in the input
multimedia stream.
</p>
<p>The information for each single frame is printed within a dedicated
section with name &quot;FRAME&quot; or &quot;SUBTITLE&quot;.
</p>
</dd>
<dt><samp class="option">-show_log <var class="var">loglevel</var></samp></dt>
<dd><p>Show logging information from the decoder about each frame according to
the value set in <var class="var">loglevel</var>, (see <code class="code">-loglevel</code>). This option requires <code class="code">-show_frames</code>.
</p>
<p>The information for each log message is printed within a dedicated
section with name &quot;LOG&quot;.
</p>
</dd>
<dt><samp class="option">-show_streams</samp></dt>
<dd><p>Show information about each media stream contained in the input
multimedia stream.
</p>
<p>Each media stream information is printed within a dedicated section
with name &quot;STREAM&quot;.
</p>
</dd>
<dt><samp class="option">-show_programs</samp></dt>
<dd><p>Show information about programs and their streams contained in the input
multimedia stream.
</p>
<p>Each media stream information is printed within a dedicated section
with name &quot;PROGRAM_STREAM&quot;.
</p>
</dd>
<dt><samp class="option">-show_stream_groups</samp></dt>
<dd><p>Show information about stream groups and their streams contained in the
input multimedia stream.
</p>
<p>Each media stream information is printed within a dedicated section
with name &quot;STREAM_GROUP_STREAM&quot;.
</p>
</dd>
<dt><samp class="option">-show_chapters</samp></dt>
<dd><p>Show information about chapters stored in the format.
</p>
<p>Each chapter is printed within a dedicated section with name &quot;CHAPTER&quot;.
</p>
</dd>
<dt><samp class="option">-count_frames</samp></dt>
<dd><p>Count the number of frames per stream and report it in the
corresponding stream section.
</p>
</dd>
<dt><samp class="option">-count_packets</samp></dt>
<dd><p>Count the number of packets per stream and report it in the
corresponding stream section.
</p>
</dd>
<dt><samp class="option">-read_intervals <var class="var">read_intervals</var></samp></dt>
<dd>
<p>Read only the specified intervals. <var class="var">read_intervals</var> must be a
sequence of interval specifications separated by &quot;,&quot;.
<code class="command">ffprobe</code> will seek to the interval starting point, and will
continue reading from that.
</p>
<p>Each interval is specified by two optional parts, separated by &quot;%&quot;.
</p>
<p>The first part specifies the interval start position. It is
interpreted as an absolute position, or as a relative offset from the
current position if it is preceded by the &quot;+&quot; character. If this first
part is not specified, no seeking will be performed when reading this
interval.
</p>
<p>The second part specifies the interval end position. It is interpreted
as an absolute position, or as a relative offset from the current
position if it is preceded by the &quot;+&quot; character. If the offset
specification starts with &quot;#&quot;, it is interpreted as the number of
packets to read (not including the flushing packets) from the interval
start. If no second part is specified, the program will read until the
end of the input.
</p>
<p>Note that seeking is not accurate, thus the actual interval start
point may be different from the specified position. Also, when an
interval duration is specified, the absolute end time will be computed
by adding the duration to the interval start point found by seeking
the file, rather than to the specified start value.
</p>
<p>The formal syntax is given by:
</p><div class="example">
<pre class="example-preformatted"><var class="var">INTERVAL</var>  ::= [<var class="var">START</var>|+<var class="var">START_OFFSET</var>][%[<var class="var">END</var>|+<var class="var">END_OFFSET</var>]]
<var class="var">INTERVALS</var> ::= <var class="var">INTERVAL</var>[,<var class="var">INTERVALS</var>]
</pre></div>

<p>A few examples follow.
</p><ul class="itemize mark-bullet">
<li>Seek to time 10, read packets until 20 seconds after the found seek
point, then seek to position <code class="code">01:30</code> (1 minute and thirty
seconds) and read packets until position <code class="code">01:45</code>.
<div class="example">
<pre class="example-preformatted">10%+20,01:30%01:45
</pre></div>

</li><li>Read only 42 packets after seeking to position <code class="code">01:23</code>:
<div class="example">
<pre class="example-preformatted">01:23%+#42
</pre></div>

</li><li>Read only the first 20 seconds from the start:
<div class="example">
<pre class="example-preformatted">%+20
</pre></div>

</li><li>Read from the start until position <code class="code">02:30</code>:
<div class="example">
<pre class="example-preformatted">%02:30
</pre></div>
</li></ul>

</dd>
<dt><samp class="option">-show_private_data, -private</samp></dt>
<dd><p>Show private data, that is data depending on the format of the
particular shown element.
This option is enabled by default, but you may need to disable it
for specific uses, for example when creating XSD-compliant XML output.
</p>
</dd>
<dt><samp class="option">-show_program_version</samp></dt>
<dd><p>Show information related to program version.
</p>
<p>Version information is printed within a section with name
&quot;PROGRAM_VERSION&quot;.
</p>
</dd>
<dt><samp class="option">-show_library_versions</samp></dt>
<dd><p>Show information related to library versions.
</p>
<p>Version information for each library is printed within a section with
name &quot;LIBRARY_VERSION&quot;.
</p>
</dd>
<dt><samp class="option">-show_versions</samp></dt>
<dd><p>Show information related to program and library versions. This is the
equivalent of setting both <samp class="option">-show_program_version</samp> and
<samp class="option">-show_library_versions</samp> options.
</p>
</dd>
<dt><samp class="option">-show_pixel_formats</samp></dt>
<dd><p>Show information about all pixel formats supported by FFmpeg.
</p>
<p>Pixel format information for each format is printed within a section
with name &quot;PIXEL_FORMAT&quot;.
</p>
</dd>
<dt><samp class="option">-show_optional_fields <var class="var">value</var></samp></dt>
<dd><p>Some writers viz. JSON and XML, omit the printing of fields with invalid or non-applicable values,
while other writers always print them. This option enables one to control this behaviour.
Valid values are <code class="code">always</code>/<code class="code">1</code>, <code class="code">never</code>/<code class="code">0</code> and <code class="code">auto</code>/<code class="code">-1</code>.
Default is <var class="var">auto</var>.
</p>
</dd>
<dt><samp class="option">-bitexact</samp></dt>
<dd><p>Force bitexact output, useful to produce output which is not dependent
on the specific build.
</p>
</dd>
<dt><samp class="option">-i <var class="var">input_url</var></samp></dt>
<dd><p>Read <var class="var">input_url</var>.
</p>
</dd>
<dt><samp class="option">-o <var class="var">output_url</var></samp></dt>
<dd><p>Write output to <var class="var">output_url</var>. If not specified, the output is sent
to stdout.
</p>
</dd>
</dl>

</div>
</div>
<div class="chapter-level-extent" id="Writers">
<h2 class="chapter"><span>4 Writers<a class="copiable-link" href="#Writers"> &para;</a></span></h2>

<p>A writer defines the output format adopted by <code class="command">ffprobe</code>, and will be
used for printing all the parts of the output.
</p>
<p>A writer may accept one or more arguments, which specify the options
to adopt. The options are specified as a list of <var class="var">key</var>=<var class="var">value</var>
pairs, separated by &quot;:&quot;.
</p>
<p>All writers support the following options:
</p>
<dl class="table">
<dt><samp class="option">string_validation, sv</samp></dt>
<dd><p>Set string validation mode.
</p>
<p>The following values are accepted.
</p><dl class="table">
<dt>&lsquo;<samp class="samp">fail</samp>&rsquo;</dt>
<dd><p>The writer will fail immediately in case an invalid string (UTF-8)
sequence or code point is found in the input. This is especially
useful to validate input metadata.
</p>
</dd>
<dt>&lsquo;<samp class="samp">ignore</samp>&rsquo;</dt>
<dd><p>Any validation error will be ignored. This will result in possibly
broken output, especially with the json or xml writer.
</p>
</dd>
<dt>&lsquo;<samp class="samp">replace</samp>&rsquo;</dt>
<dd><p>The writer will substitute invalid UTF-8 sequences or code points with
the string specified with the <samp class="option">string_validation_replacement</samp>.
</p></dd>
</dl>

<p>Default value is &lsquo;<samp class="samp">replace</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">string_validation_replacement, svr</samp></dt>
<dd><p>Set replacement string to use in case <samp class="option">string_validation</samp> is
set to &lsquo;<samp class="samp">replace</samp>&rsquo;.
</p>
<p>In case the option is not specified, the writer will assume the empty
string, that is it will remove the invalid sequences from the input
strings.
</p></dd>
</dl>

<p>A description of the currently available writers follows.
</p>
<ul class="mini-toc">
<li><a href="#default" accesskey="1">default</a></li>
<li><a href="#compact_002c-csv" accesskey="2">compact, csv</a></li>
<li><a href="#flat" accesskey="3">flat</a></li>
<li><a href="#ini" accesskey="4">ini</a></li>
<li><a href="#json" accesskey="5">json</a></li>
<li><a href="#xml" accesskey="6">xml</a></li>
</ul>
<div class="section-level-extent" id="default">
<h3 class="section"><span>4.1 default<a class="copiable-link" href="#default"> &para;</a></span></h3>
<p>Default format.
</p>
<p>Print each section in the form:
</p><div class="example">
<pre class="example-preformatted">[SECTION]
key1=val1
...
keyN=valN
[/SECTION]
</pre></div>

<p>Metadata tags are printed as a line in the corresponding FORMAT, STREAM,
STREAM_GROUP_STREAM or PROGRAM_STREAM section, and are prefixed by the
string &quot;TAG:&quot;.
</p>
<p>A description of the accepted options follows.
</p>
<dl class="table">
<dt><samp class="option">nokey, nk</samp></dt>
<dd><p>If set to 1 specify not to print the key of each field. Default value
is 0.
</p>
</dd>
<dt><samp class="option">noprint_wrappers, nw</samp></dt>
<dd><p>If set to 1 specify not to print the section header and footer.
Default value is 0.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="compact_002c-csv">
<h3 class="section"><span>4.2 compact, csv<a class="copiable-link" href="#compact_002c-csv"> &para;</a></span></h3>
<p>Compact and CSV format.
</p>
<p>The <code class="code">csv</code> writer is equivalent to <code class="code">compact</code>, but supports
different defaults.
</p>
<p>Each section is printed on a single line.
If no option is specified, the output has the form:
</p><div class="example">
<pre class="example-preformatted">section|key1=val1| ... |keyN=valN
</pre></div>

<p>Metadata tags are printed in the corresponding &quot;format&quot; or &quot;stream&quot;
section. A metadata tag key, if printed, is prefixed by the string
&quot;tag:&quot;.
</p>
<p>The description of the accepted options follows.
</p>
<dl class="table">
<dt><samp class="option">item_sep, s</samp></dt>
<dd><p>Specify the character to use for separating fields in the output line.
It must be a single printable character, it is &quot;|&quot; by default (&quot;,&quot; for
the <code class="code">csv</code> writer).
</p>
</dd>
<dt><samp class="option">nokey, nk</samp></dt>
<dd><p>If set to 1 specify not to print the key of each field. Its default
value is 0 (1 for the <code class="code">csv</code> writer).
</p>
</dd>
<dt><samp class="option">escape, e</samp></dt>
<dd><p>Set the escape mode to use, default to &quot;c&quot; (&quot;csv&quot; for the <code class="code">csv</code>
writer).
</p>
<p>It can assume one of the following values:
</p><dl class="table">
<dt><samp class="option">c</samp></dt>
<dd><p>Perform C-like escaping. Strings containing a newline (&lsquo;<samp class="samp">\n</samp>&rsquo;), carriage
return (&lsquo;<samp class="samp">\r</samp>&rsquo;), a tab (&lsquo;<samp class="samp">\t</samp>&rsquo;), a form feed (&lsquo;<samp class="samp">\f</samp>&rsquo;), the escaping
character (&lsquo;<samp class="samp">\</samp>&rsquo;) or the item separator character <var class="var">SEP</var> are escaped
using C-like fashioned escaping, so that a newline is converted to the
sequence &lsquo;<samp class="samp">\n</samp>&rsquo;, a carriage return to &lsquo;<samp class="samp">\r</samp>&rsquo;, &lsquo;<samp class="samp">\</samp>&rsquo; to &lsquo;<samp class="samp">\\</samp>&rsquo; and
the separator <var class="var">SEP</var> is converted to &lsquo;<samp class="samp">\<var class="var">SEP</var></samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">csv</samp></dt>
<dd><p>Perform CSV-like escaping, as described in RFC4180.  Strings
containing a newline (&lsquo;<samp class="samp">\n</samp>&rsquo;), a carriage return (&lsquo;<samp class="samp">\r</samp>&rsquo;), a double quote
(&lsquo;<samp class="samp">&quot;</samp>&rsquo;), or <var class="var">SEP</var> are enclosed in double-quotes.
</p>
</dd>
<dt><samp class="option">none</samp></dt>
<dd><p>Perform no escaping.
</p></dd>
</dl>

</dd>
<dt><samp class="option">print_section, p</samp></dt>
<dd><p>Print the section name at the beginning of each line if the value is
<code class="code">1</code>, disable it with value set to <code class="code">0</code>. Default value is
<code class="code">1</code>.
</p>
</dd>
</dl>

</div>
<div class="section-level-extent" id="flat">
<h3 class="section"><span>4.3 flat<a class="copiable-link" href="#flat"> &para;</a></span></h3>
<p>Flat format.
</p>
<p>A free-form output where each line contains an explicit key=value, such as
&quot;streams.stream.3.tags.foo=bar&quot;. The output is shell escaped, so it can be
directly embedded in sh scripts as long as the separator character is an
alphanumeric character or an underscore (see <var class="var">sep_char</var> option).
</p>
<p>The description of the accepted options follows.
</p>
<dl class="table">
<dt><samp class="option">sep_char, s</samp></dt>
<dd><p>Separator character used to separate the chapter, the section name, IDs and
potential tags in the printed field key.
</p>
<p>Default value is &lsquo;<samp class="samp">.</samp>&rsquo;.
</p>
</dd>
<dt><samp class="option">hierarchical, h</samp></dt>
<dd><p>Specify if the section name specification should be hierarchical. If
set to 1, and if there is more than one section in the current
chapter, the section name will be prefixed by the name of the
chapter. A value of 0 will disable this behavior.
</p>
<p>Default value is 1.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="ini">
<h3 class="section"><span>4.4 ini<a class="copiable-link" href="#ini"> &para;</a></span></h3>
<p>INI format output.
</p>
<p>Print output in an INI based format.
</p>
<p>The following conventions are adopted:
</p>
<ul class="itemize mark-bullet">
<li>all key and values are UTF-8
</li><li>&lsquo;<samp class="samp">.</samp>&rsquo; is the subgroup separator
</li><li>newline, &lsquo;<samp class="samp">\t</samp>&rsquo;, &lsquo;<samp class="samp">\f</samp>&rsquo;, &lsquo;<samp class="samp">\b</samp>&rsquo; and the following characters are
escaped
</li><li>&lsquo;<samp class="samp">\</samp>&rsquo; is the escape character
</li><li>&lsquo;<samp class="samp">#</samp>&rsquo; is the comment indicator
</li><li>&lsquo;<samp class="samp">=</samp>&rsquo; is the key/value separator
</li><li>&lsquo;<samp class="samp">:</samp>&rsquo; is not used but usually parsed as key/value separator
</li></ul>

<p>This writer accepts options as a list of <var class="var">key</var>=<var class="var">value</var> pairs,
separated by &lsquo;<samp class="samp">:</samp>&rsquo;.
</p>
<p>The description of the accepted options follows.
</p>
<dl class="table">
<dt><samp class="option">hierarchical, h</samp></dt>
<dd><p>Specify if the section name specification should be hierarchical. If
set to 1, and if there is more than one section in the current
chapter, the section name will be prefixed by the name of the
chapter. A value of 0 will disable this behavior.
</p>
<p>Default value is 1.
</p></dd>
</dl>

</div>
<div class="section-level-extent" id="json">
<h3 class="section"><span>4.5 json<a class="copiable-link" href="#json"> &para;</a></span></h3>
<p>JSON based format.
</p>
<p>Each section is printed using JSON notation.
</p>
<p>The description of the accepted options follows.
</p>
<dl class="table">
<dt><samp class="option">compact, c</samp></dt>
<dd><p>If set to 1 enable compact output, that is each section will be
printed on a single line. Default value is 0.
</p></dd>
</dl>

<p>For more information about JSON, see <a class="url" href="http://www.json.org/">http://www.json.org/</a>.
</p>
</div>
<div class="section-level-extent" id="xml">
<h3 class="section"><span>4.6 xml<a class="copiable-link" href="#xml"> &para;</a></span></h3>
<p>XML based format.
</p>
<p>The XML output is described in the XML schema description file
<samp class="file">ffprobe.xsd</samp> installed in the FFmpeg datadir.
</p>
<p>An updated version of the schema can be retrieved at the url
<a class="url" href="http://www.ffmpeg.org/schema/ffprobe.xsd">http://www.ffmpeg.org/schema/ffprobe.xsd</a>, which redirects to the
latest schema committed into the FFmpeg development source code tree.
</p>
<p>Note that the output issued will be compliant to the
<samp class="file">ffprobe.xsd</samp> schema only when no special global output options
(<samp class="option">unit</samp>, <samp class="option">prefix</samp>, <samp class="option">byte_binary_prefix</samp>,
<samp class="option">sexagesimal</samp> etc.) are specified.
</p>
<p>The description of the accepted options follows.
</p>
<dl class="table">
<dt><samp class="option">fully_qualified, q</samp></dt>
<dd><p>If set to 1 specify if the output should be fully qualified. Default
value is 0.
This is required for generating an XML file which can be validated
through an XSD file.
</p>
</dd>
<dt><samp class="option">xsd_strict, x</samp></dt>
<dd><p>If set to 1 perform more checks for ensuring that the output is XSD
compliant. Default value is 0.
This option automatically sets <samp class="option">fully_qualified</samp> to 1.
</p></dd>
</dl>

<p>For more information about the XML format, see
<a class="url" href="https://www.w3.org/XML/">https://www.w3.org/XML/</a>.
</p>
</div>
</div>
<div class="chapter-level-extent" id="Timecode">
<h2 class="chapter"><span>5 Timecode<a class="copiable-link" href="#Timecode"> &para;</a></span></h2>

<p><code class="command">ffprobe</code> supports Timecode extraction:
</p>
<ul class="itemize mark-bullet">
<li>MPEG1/2 timecode is extracted from the GOP, and is available in the video
stream details (<samp class="option">-show_streams</samp>, see <var class="var">timecode</var>).

</li><li>MOV timecode is extracted from tmcd track, so is available in the tmcd
stream metadata (<samp class="option">-show_streams</samp>, see <var class="var">TAG:timecode</var>).

</li><li>DV, GXF and AVI timecodes are available in format metadata
(<samp class="option">-show_format</samp>, see <var class="var">TAG:timecode</var>).

</li></ul>


</div>
<div class="chapter-level-extent" id="See-Also">
<h2 class="chapter"><span>6 See Also<a class="copiable-link" href="#See-Also"> &para;</a></span></h2>

<p><a class="url" href="ffprobe-all.html">ffprobe-all</a>,
<a class="url" href="ffmpeg.html">ffmpeg</a>, <a class="url" href="ffplay.html">ffplay</a>,
<a class="url" href="ffmpeg-utils.html">ffmpeg-utils</a>,
<a class="url" href="ffmpeg-scaler.html">ffmpeg-scaler</a>,
<a class="url" href="ffmpeg-resampler.html">ffmpeg-resampler</a>,
<a class="url" href="ffmpeg-codecs.html">ffmpeg-codecs</a>,
<a class="url" href="ffmpeg-bitstream-filters.html">ffmpeg-bitstream-filters</a>,
<a class="url" href="ffmpeg-formats.html">ffmpeg-formats</a>,
<a class="url" href="ffmpeg-devices.html">ffmpeg-devices</a>,
<a class="url" href="ffmpeg-protocols.html">ffmpeg-protocols</a>,
<a class="url" href="ffmpeg-filters.html">ffmpeg-filters</a>
</p>

</div>
<div class="chapter-level-extent" id="Authors">
<h2 class="chapter"><span>7 Authors<a class="copiable-link" href="#Authors"> &para;</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code class="command">git log</code> in the FFmpeg source directory, or browsing the
online repository at <a class="url" href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp class="file">MAINTAINERS</samp> in the source code tree.
</p>

</div>
</div>
      <p style="font-size: small;">
        This document was generated using <a class="uref" href="https://www.gnu.org/software/texinfo/"><em class="emph">makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
