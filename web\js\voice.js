// 语音功能管理类
class VoiceManager {
    constructor() {
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
        this.stream = null;
        this.audioContext = null;
        this.analyser = null;
        this.dataArray = null;
        this.animationId = null;
        this.currentVoice = 'longxiaochun_v2';

        // 连续录音相关属性
        this.isContinuousRecording = false;
        this.continuousChunks = [];
        this.segmentStartTime = 0;

        // 分段录音模式标志
        this.isSegmentRecording = false;

        // 优先使用Paraformer-8k-v2支持的webm格式
        this.supportedFormats = [
            'audio/webm;codecs=opus',  // WebM with Opus (Paraformer-8k-v2支持)
            'audio/webm',              // WebM fallback
            'audio/wav',               // WAV兼容格式
            'audio/mp4'                // MP4/AAC
        ];
        this.selectedFormat = this.getSupportedFormat();
    }

    // 检测浏览器支持的音频格式，优先选择ASR兼容的格式
    getSupportedFormat() {
        for (const format of this.supportedFormats) {
            if (MediaRecorder.isTypeSupported(format)) {
                console.log(`选择音频格式: ${format}`);
                return format;
            }
        }
        console.log('使用默认格式: audio/webm');
        return 'audio/webm'; // 默认格式
    }

    // 初始化音频权限
    async initializeAudio() {
        try {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('您的浏览器不支持音频录制功能');
            }

            // 为Paraformer-8k-v2优化音频设置
            this.stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 8000,  // 使用8kHz采样率匹配paraformer-realtime-8k-v2
                    channelCount: 1    // 单声道
                }
            });

            // 创建音频上下文用于可视化
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.analyser = this.audioContext.createAnalyser();
            const source = this.audioContext.createMediaStreamSource(this.stream);
            source.connect(this.analyser);
            
            this.analyser.fftSize = 256;
            this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);

            return true;
        } catch (error) {
            console.error('音频初始化失败:', error);
            throw error;
        }
    }

    // 开始录音
    async startRecording() {
        try {
            if (!this.stream) {
                await this.initializeAudio();
            }

            this.audioChunks = [];

            // 尝试使用更兼容的录音方式
            if (this.selectedFormat.includes('wav') && this.audioContext) {
                // 使用Web Audio API录制PCM数据，然后转换为WAV
                return this.startWebAudioRecording();
            } else {
                // 使用MediaRecorder API
                return this.startMediaRecorderRecording();
            }
        } catch (error) {
            console.error('开始录音失败:', error);
            throw error;
        }
    }

    // 使用MediaRecorder录音
    async startMediaRecorderRecording() {
        // 创建MediaRecorder时添加更多配置
        const options = {
            mimeType: this.selectedFormat
        };

        // 如果支持，添加音频比特率配置
        if (this.selectedFormat.includes('webm')) {
            options.audioBitsPerSecond = 128000; // 128kbps
        }

        this.mediaRecorder = new MediaRecorder(this.stream, options);

        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data && event.data.size > 0) {
                console.log(`录音数据块: ${event.data.size} bytes, 总块数: ${this.audioChunks.length + 1}`);
                this.audioChunks.push(event.data);
            }
        };

        this.mediaRecorder.onstop = () => {
            console.log(`录音完成，共 ${this.audioChunks.length} 个数据块，分段录音模式: ${this.isSegmentRecording}`);
            this.onRecordingComplete();
        };

        this.mediaRecorder.onerror = (event) => {
            console.error('录音错误:', event.error);
        };

        // 开始录音，设置时间片段为100ms
        this.mediaRecorder.start(100);
        this.isRecording = true;

        // 开始音频可视化
        this.startVisualization();

        console.log(`开始MediaRecorder录音，格式: ${this.selectedFormat}`);
        return true;
    }

    // 使用Web Audio API录音（生成WAV格式）
    async startWebAudioRecording() {
        try {
            // 创建ScriptProcessorNode来处理音频数据
            this.scriptProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);
            this.audioChunks = []; // 存储PCM数据

            const source = this.audioContext.createMediaStreamSource(this.stream);
            source.connect(this.scriptProcessor);
            this.scriptProcessor.connect(this.audioContext.destination);

            this.scriptProcessor.onaudioprocess = (event) => {
                if (this.isRecording) {
                    const inputBuffer = event.inputBuffer;
                    const inputData = inputBuffer.getChannelData(0);

                    // 将Float32Array转换为Int16Array (PCM 16-bit)
                    const pcmData = new Int16Array(inputData.length);
                    for (let i = 0; i < inputData.length; i++) {
                        pcmData[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32768));
                    }

                    this.audioChunks.push(pcmData);
                }
            };

            this.isRecording = true;
            this.startVisualization();

            console.log('开始Web Audio API录音，将生成WAV格式');
            return true;

        } catch (error) {
            console.error('Web Audio API录音失败:', error);
            // 回退到MediaRecorder
            return this.startMediaRecorderRecording();
        }
    }

    // 停止录音
    stopRecording() {
        if (this.isRecording) {
            this.isRecording = false;
            this.isContinuousRecording = false;
            this.stopVisualization();

            if (this.mediaRecorder) {
                // MediaRecorder录音
                this.mediaRecorder.stop();
            } else if (this.scriptProcessor) {
                // Web Audio API录音
                this.scriptProcessor.disconnect();
                this.onWebAudioRecordingComplete();
            }
        }
    }

    // 开始连续录音
    async startContinuousRecording() {
        try {
            if (!this.stream) {
                await this.initializeAudio();
            }

            this.continuousChunks = [];
            this.segmentStartTime = Date.now();
            this.isContinuousRecording = true;

            // 使用MediaRecorder进行连续录音
            return this.startContinuousMediaRecorderRecording();
        } catch (error) {
            console.error('开始连续录音失败:', error);
            throw error;
        }
    }

    // 使用MediaRecorder进行连续录音
    async startContinuousMediaRecorderRecording() {
        const options = {
            mimeType: this.selectedFormat
        };

        if (this.selectedFormat.includes('webm')) {
            options.audioBitsPerSecond = 128000;
        }

        this.mediaRecorder = new MediaRecorder(this.stream, options);

        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data && event.data.size > 0) {
                const chunk = {
                    data: event.data,
                    timestamp: Date.now()
                };
                this.continuousChunks.push(chunk);
                console.log(`连续录音数据块: ${event.data.size} bytes, 总块数: ${this.continuousChunks.length}`);
            }
        };

        this.mediaRecorder.onstop = () => {
            console.log('连续录音停止');
        };

        this.mediaRecorder.onerror = (event) => {
            console.error('连续录音错误:', event.error);
        };

        // 开始录音，设置较小的时间片段以便实时获取数据
        this.mediaRecorder.start(2000); // 每2秒生成一个数据块
        this.isRecording = true;

        // 开始音频可视化
        this.startVisualization();

        console.log(`开始连续录音，格式: ${this.selectedFormat}`);
        return true;
    }

    // 获取当前录音片段（使用最新的单个音频块）
    async getCurrentSegment() {
        console.log(`getCurrentSegment调用 - 连续录音状态: ${this.isContinuousRecording}, 音频块数量: ${this.continuousChunks.length}`);

        if (!this.isContinuousRecording) {
            console.log('连续录音未激活');
            return null;
        }

        if (this.continuousChunks.length === 0) {
            console.log('没有音频块可用');
            return null;
        }

        try {
            // 获取最新的音频块（避免合并WebM格式导致的格式损坏）
            const latestChunk = this.continuousChunks[this.continuousChunks.length - 1];

            if (!latestChunk || !latestChunk.data) {
                console.log('最新音频块无效');
                return null;
            }

            const audioBlob = latestChunk.data;
            console.log(`使用最新音频块: ${audioBlob.size} bytes, 格式: ${this.selectedFormat}`);

            if (audioBlob.size < 1000) {
                console.log('音频片段太小，跳过');
                return null;
            }

            // 转换为base64
            const audioBase64 = await this.blobToBase64(audioBlob);
            const audioFormat = this.mapToASRFormat(this.selectedFormat);

            console.log(`获取音频片段成功: ${audioBlob.size} bytes, base64长度: ${audioBase64.length}, 格式: ${audioFormat}`);

            // 清理旧的音频块以节省内存（保留最近的5个块）
            if (this.continuousChunks.length > 5) {
                this.continuousChunks = this.continuousChunks.slice(-5);
                console.log(`清理旧音频块，保留最近5个块`);
            }

            return {
                audioBase64,
                audioFormat
            };

        } catch (error) {
            console.error('获取音频片段失败:', error);
            return null;
        }
    }

    // Web Audio API录音完成处理
    onWebAudioRecordingComplete() {
        try {
            if (this.audioChunks.length === 0) {
                console.error('没有录制到PCM数据');
                ErrorHandler.show('录音失败：没有录制到音频数据');
                return;
            }

            // 合并所有PCM数据
            let totalLength = 0;
            for (const chunk of this.audioChunks) {
                totalLength += chunk.length;
            }

            const mergedPCM = new Int16Array(totalLength);
            let offset = 0;
            for (const chunk of this.audioChunks) {
                mergedPCM.set(chunk, offset);
                offset += chunk.length;
            }

            console.log(`合并PCM数据: ${mergedPCM.length} 采样点`);

            // 转换为WAV格式
            const wavBlob = this.createWAVBlob(mergedPCM, 16000, 1);
            console.log(`生成WAV文件: ${wavBlob.size} bytes`);

            this.processAudioBlob(wavBlob);

        } catch (error) {
            console.error('Web Audio录音处理失败:', error);
            ErrorHandler.show('录音处理失败: ' + error.message);
        }
    }

    // 创建WAV格式的Blob
    createWAVBlob(pcmData, sampleRate, channels) {
        const length = pcmData.length;
        const buffer = new ArrayBuffer(44 + length * 2);
        const view = new DataView(buffer);

        // WAV文件头
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, channels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * channels * 2, true);
        view.setUint16(32, channels * 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, length * 2, true);

        // PCM数据
        let offset = 44;
        for (let i = 0; i < length; i++) {
            view.setInt16(offset, pcmData[i], true);
            offset += 2;
        }

        return new Blob([buffer], { type: 'audio/wav' });
    }

    // 录音完成处理
    onRecordingComplete() {
        if (this.audioChunks.length === 0) {
            console.error('没有录音数据');
            // 分段录音模式下不显示错误
            if (!this.isSegmentRecording) {
                ErrorHandler.show('录音失败：没有录制到音频数据');
            }
            return;
        }

        const audioBlob = new Blob(this.audioChunks, { type: this.selectedFormat });
        console.log(`音频Blob创建: 大小=${audioBlob.size} bytes, 类型=${audioBlob.type}`);

        if (audioBlob.size === 0) {
            console.error('音频Blob为空');
            // 分段录音模式下不显示错误
            if (!this.isSegmentRecording) {
                ErrorHandler.show('录音失败：音频数据为空');
            }
            return;
        }

        if (audioBlob.size < 1000) { // 小于1KB可能是无效录音
            console.warn(`音频数据较小: ${audioBlob.size} bytes`);
            // 分段录音模式下不显示错误
            if (!this.isSegmentRecording) {
                ErrorHandler.show('录音时间太短，请重新录制');
            }
            return;
        }

        // 分段录音模式下不触发onAudioReady回调
        if (!this.isSegmentRecording) {
            this.processAudioBlob(audioBlob);
        }
    }

    // 处理音频数据
    async processAudioBlob(audioBlob) {
        try {
            // 将音频转换为base64
            const audioBase64 = await this.blobToBase64(audioBlob);

            // 智能确定音频格式，映射到通义千问ASR支持的格式
            let audioFormat = this.mapToASRFormat(this.selectedFormat);

            console.log(`音频格式映射: ${this.selectedFormat} -> ${audioFormat}`);
            console.log(`音频数据大小: ${audioBase64.length} 字符`);

            // 触发语音处理事件
            this.onAudioReady(audioBase64, audioFormat);

        } catch (error) {
            console.error('音频处理失败:', error);
            ErrorHandler.show('音频处理失败: ' + error.message);
        }
    }

    // 将浏览器音频格式映射到Paraformer ASR支持的格式
    mapToASRFormat(browserFormat) {
        // Paraformer-8k-v2支持的格式：pcm、wav、mp3、opus、speex、aac、amr、webm

        if (browserFormat.includes('webm')) {
            // WebM格式直接支持，优先使用
            console.log('使用WebM格式，Paraformer-8k-v2原生支持');
            return 'webm';
        } else if (browserFormat.includes('wav')) {
            return 'wav';
        } else if (browserFormat.includes('mp4')) {
            return 'mp4';   // MP4 -> 让后端映射为aac
        } else if (browserFormat.includes('mp3')) {
            return 'mp3';
        } else {
            // 默认使用webm格式（Paraformer-8k-v2支持）
            console.log(`未知音频格式: ${browserFormat}，使用WebM作为默认格式`);
            return 'webm';
        }
    }

    // 将Blob转换为Base64
    blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                try {
                    const result = reader.result;
                    if (!result || typeof result !== 'string') {
                        throw new Error('FileReader result is invalid');
                    }

                    const base64 = result.split(',')[1]; // 移除data:audio/...;base64,前缀
                    if (!base64 || base64.length === 0) {
                        throw new Error('Base64 data is empty');
                    }

                    console.log(`Base64编码完成: ${base64.length} 字符`);
                    resolve(base64);
                } catch (error) {
                    console.error('Base64编码失败:', error);
                    reject(error);
                }
            };
            reader.onerror = (error) => {
                console.error('FileReader错误:', error);
                reject(error);
            };
            reader.readAsDataURL(blob);
        });
    }

    // 音频准备就绪回调（需要在外部设置）
    onAudioReady(audioBase64, audioFormat) {
        console.log('音频准备就绪:', audioFormat, audioBase64.length);
        // 这个方法会在外部被重写
    }

    // 获取最后一次录音的数据（用于分段录音）
    async getLastRecordingData() {
        if (this.audioChunks.length === 0) {
            console.log('没有录音数据');
            return null;
        }

        try {
            const audioBlob = new Blob(this.audioChunks, { type: this.selectedFormat });
            console.log(`获取录音数据: ${audioBlob.size} bytes, 格式: ${this.selectedFormat}`);

            if (audioBlob.size < 100) {  // 降低最小大小要求
                console.log('录音数据太小:', audioBlob.size, 'bytes');
                return null;
            }

            console.log('录音数据大小检查通过:', audioBlob.size, 'bytes');

            // 转换为base64
            const audioBase64 = await this.blobToBase64(audioBlob);
            const audioFormat = this.mapToASRFormat(this.selectedFormat);

            console.log(`录音数据转换完成: base64长度: ${audioBase64.length}, 格式: ${audioFormat}`);

            return {
                audioBase64,
                audioFormat
            };

        } catch (error) {
            console.error('获取录音数据失败:', error);
            return null;
        }
    }

    // 开始音频可视化
    startVisualization() {
        const visualize = () => {
            if (!this.isRecording) return;
            
            this.animationId = requestAnimationFrame(visualize);
            
            if (this.analyser && this.dataArray) {
                this.analyser.getByteFrequencyData(this.dataArray);
                this.updateVisualization(this.dataArray);
            }
        };
        visualize();
    }

    // 停止音频可视化
    stopVisualization() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }

    // 更新可视化效果
    updateVisualization(dataArray) {
        // 计算音频强度
        const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
        const intensity = average / 255;

        // 更新录音按钮的视觉效果（只改变阴影，不改变位置和大小）
        const recordBtn = document.querySelector('.voice-record-btn');
        if (recordBtn && this.isRecording) {
            recordBtn.style.boxShadow = `0 0 ${20 + intensity * 30}px rgba(59, 130, 246, ${0.5 + intensity * 0.5})`;
        }
    }

    // 播放音频
    async playAudio(audioBase64, audioFormat = 'mp3') {
        try {
            // 创建音频URL
            const audioData = atob(audioBase64);
            const audioArray = new Uint8Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                audioArray[i] = audioData.charCodeAt(i);
            }
            
            const audioBlob = new Blob([audioArray], { type: `audio/${audioFormat}` });
            const audioUrl = URL.createObjectURL(audioBlob);
            
            // 创建音频元素并播放
            const audio = new Audio(audioUrl);
            
            return new Promise((resolve, reject) => {
                audio.onended = () => {
                    URL.revokeObjectURL(audioUrl);
                    resolve();
                };
                audio.onerror = (error) => {
                    URL.revokeObjectURL(audioUrl);
                    reject(error);
                };
                audio.play();
            });
            
        } catch (error) {
            console.error('音频播放失败:', error);
            throw error;
        }
    }

    // 设置当前音色
    setVoice(voice) {
        this.currentVoice = voice;
    }

    // 获取当前音色
    getVoice() {
        return this.currentVoice;
    }

    // 清理资源
    cleanup() {
        this.stopRecording();
        this.stopVisualization();

        if (this.scriptProcessor) {
            this.scriptProcessor.disconnect();
            this.scriptProcessor = null;
        }

        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
    }
}

// 创建全局语音管理器实例
const voiceManager = new VoiceManager();

// 弹窗位置管理器
class ModalPositionManager {
    // 计算对话区域的中央位置
    static getDialogCenterPosition(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            return { left: '50%', top: '40%' };
        }

        const containerRect = container.getBoundingClientRect();
        const messagesContainer = container.querySelector('.chat-messages');

        if (messagesContainer) {
            const messagesRect = messagesContainer.getBoundingClientRect();

            // 计算对话区域的中央位置
            const centerX = messagesRect.left + messagesRect.width / 2;
            const centerY = messagesRect.top + messagesRect.height / 2;

            return {
                left: centerX + 'px',
                top: Math.max(200, centerY - 100) + 'px' // 确保不会太靠上
            };
        }

        // 回退到容器中央
        const centerX = containerRect.left + containerRect.width / 2;
        const centerY = containerRect.top + containerRect.height / 2;

        return {
            left: centerX + 'px',
            top: Math.max(200, centerY - 100) + 'px'
        };
    }

    // 创建统一样式的弹窗
    static createModal(id, content, containerId = 'conversationChat') {
        // 移除已存在的弹窗
        const existingModal = document.getElementById(id);
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.id = id;
        modal.innerHTML = content;

        // 获取统一的位置
        const position = this.getDialogCenterPosition(containerId);

        // 应用统一的样式
        modal.style.cssText = `
            position: fixed;
            top: ${position.top};
            left: ${position.left};
            transform: translate(-50%, -50%);
            z-index: 1002;
            animation: modalFadeIn 0.3s ease;
        `;

        document.body.appendChild(modal);
        return modal;
    }

    // 移除弹窗
    static removeModal(id) {
        const modal = document.getElementById(id);
        if (modal) {
            modal.style.animation = 'modalFadeOut 0.3s ease';
            setTimeout(() => modal.remove(), 300);
        }
    }
}

// 语音UI组件管理
class VoiceUI {
    static createVoiceButton(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return null;

        // 查找输入容器和发送按钮
        const inputContainer = container.querySelector('.chat-input-container');
        const sendBtn = container.querySelector('.send-btn');
        if (!inputContainer || !sendBtn) return null;

        // 创建语音按钮
        const voiceBtn = document.createElement('button');
        voiceBtn.className = 'voice-record-btn';
        voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
        voiceBtn.title = '点击开始语音输入';

        // 添加样式 - 位于发送按钮右侧
        voiceBtn.style.cssText = `
            position: absolute;
            right: -54px;
            top: 50%;
            transform: translateY(-50%);
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease, box-shadow 0.3s ease;
            z-index: 10;
            font-size: 18px;
        `;

        // 确保输入容器有相对定位
        inputContainer.style.position = 'relative';

        // 将语音按钮添加到输入包装器中，这样它就在发送按钮旁边
        const inputWrapper = container.querySelector('.chat-input-wrapper');
        if (inputWrapper) {
            inputWrapper.style.position = 'relative';
            inputWrapper.appendChild(voiceBtn);
        } else {
            inputContainer.appendChild(voiceBtn);
        }

        return voiceBtn;
    }

    static updateVoiceButtonState(button, isRecording) {
        if (!button) return;

        if (isRecording) {
            button.innerHTML = '<i class="fas fa-microphone"></i>';
            button.style.background = '#ef4444';
            button.title = '点击停止录音';
            button.classList.add('recording');
            // 确保位置固定
            button.style.transform = 'translateY(-50%)';
        } else {
            button.innerHTML = '<i class="fas fa-microphone"></i>';
            button.style.background = '#3b82f6';
            button.title = '点击开始语音输入';
            button.classList.remove('recording');
            button.style.transform = 'translateY(-50%)';
            button.style.boxShadow = 'none';
        }
    }

    static showVoiceStatus(message, type = 'info', containerId = 'conversationChat') {
        const content = `
            <div class="voice-status-content" style="
                background: ${type === 'error' ? '#fee' : '#eff6ff'};
                color: ${type === 'error' ? '#c53030' : '#2563eb'};
                border: 1px solid ${type === 'error' ? '#fed7d7' : '#dbeafe'};
                border-radius: 12px;
                padding: 1.5rem 2rem;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
                display: flex;
                align-items: center;
                gap: 0.75rem;
                font-weight: 500;
            ">
                <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'microphone'}"></i>
                <span>${message}</span>
            </div>
        `;

        // 使用统一的弹窗管理器
        const modal = ModalPositionManager.createModal('voiceStatusModal', content, containerId);

        // 自动移除
        setTimeout(() => {
            ModalPositionManager.removeModal('voiceStatusModal');
        }, 2000);
    }
}

// 添加语音相关样式
const voiceStyles = document.createElement('style');
voiceStyles.textContent = `
    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }
        to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    }
    
    @keyframes fadeOutScale {
        from {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
        to {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }
    }
    
    .voice-status-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 500;
    }
    
    .voice-record-btn:hover {
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
        background: #2563eb !important;
    }
    
    .voice-record-btn.recording {
        animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }
`;
document.head.appendChild(voiceStyles);
