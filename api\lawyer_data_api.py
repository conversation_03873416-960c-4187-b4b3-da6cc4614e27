"""
律师数据API - 从外部数据源获取律师信息
"""

import requests
import json
import time
from typing import List, Dict, Any
from utils.logger import LOG

class LawyerDataAPI:
    """律师数据API类"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/html, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 模拟的API端点（实际使用时替换为真实API）
        self.api_endpoints = {
            'lawyers_directory': 'https://api.lawyers-directory.com/v1/lawyers',
            'legal_services': 'https://api.legal-services.com/firms',
            'law_firms_db': 'https://api.lawfirms-db.com/search'
        }
    
    def fetch_lawyers_from_api(self) -> List[Dict[str, Any]]:
        """从API获取律师数据"""
        all_lawyers = []
        
        # 尝试从多个数据源获取
        data_sources = [
            self._fetch_from_lawyers_directory,
            self._fetch_from_legal_services,
            self._fetch_from_law_firms_db
        ]
        
        for fetch_func in data_sources:
            try:
                lawyers = fetch_func()
                all_lawyers.extend(lawyers)
                time.sleep(1)  # 避免请求过于频繁
            except Exception as e:
                LOG.error(f"从数据源获取律师信息失败: {str(e)}")
                continue
        
        # 如果没有获取到数据，返回模拟数据
        if not all_lawyers:
            LOG.warning("未能从API获取数据，使用模拟数据")
            all_lawyers = self._get_mock_api_data()
        
        return all_lawyers
    
    def _fetch_from_lawyers_directory(self) -> List[Dict[str, Any]]:
        """从律师目录API获取数据（模拟实现）"""
        LOG.info("正在从律师目录API获取数据...")
        
        # 模拟API响应
        mock_response = {
            "status": "success",
            "data": [
                {
                    "firm_id": "ld_001",
                    "name": "北京市盈科律师事务所",
                    "location": "北京",
                    "specialties": ["刑事辩护", "民事诉讼", "公司法务", "知识产权"],
                    "rating": 4.7,
                    "established_year": 2001,
                    "lawyers_count": 300,
                    "description": "盈科律师事务所是一家大型综合性律师事务所，在刑事辩护和民事诉讼领域具有丰富经验。",
                    "contact_info": {
                        "phone": "010-59627888",
                        "email": "<EMAIL>",
                        "address": "北京市朝阳区金和东路20号院正大中心2号楼19-25层",
                        "website": "www.yingkelawyer.com"
                    },
                    "notable_cases": [
                        "某知名企业家刑事辩护案",
                        "大型企业知识产权纠纷案",
                        "跨国公司投资并购法律服务"
                    ],
                    "key_lawyers": [
                        {"name": "梅向荣", "title": "创始合伙人", "specialty": "刑事辩护"},
                        {"name": "杨琳", "title": "管理合伙人", "specialty": "公司法务"},
                        {"name": "李亚", "title": "合伙人", "specialty": "知识产权"}
                    ]
                },
                {
                    "firm_id": "ld_002",
                    "name": "上海市汇业律师事务所",
                    "location": "上海",
                    "specialties": ["金融法", "证券法", "房地产法", "劳动法"],
                    "rating": 4.6,
                    "established_year": 1998,
                    "lawyers_count": 200,
                    "description": "汇业律师事务所是上海知名律师事务所，在金融证券和房地产法律服务方面享有盛誉。",
                    "contact_info": {
                        "phone": "021-64471116",
                        "email": "<EMAIL>",
                        "address": "上海市徐汇区漕溪北路88号圣爱大厦2305室",
                        "website": "www.huiye-law.com"
                    },
                    "notable_cases": [
                        "多家公司IPO法律服务",
                        "大型房地产项目法律顾问",
                        "金融机构合规法律服务"
                    ],
                    "key_lawyers": [
                        {"name": "吴冬", "title": "主任", "specialty": "金融法"},
                        {"name": "李大伟", "title": "合伙人", "specialty": "证券法"},
                        {"name": "张明", "title": "合伙人", "specialty": "房地产法"}
                    ]
                }
            ]
        }
        
        return self._format_api_data(mock_response["data"], "律师目录API")
    
    def _fetch_from_legal_services(self) -> List[Dict[str, Any]]:
        """从法律服务API获取数据（模拟实现）"""
        LOG.info("正在从法律服务API获取数据...")
        
        mock_response = {
            "firms": [
                {
                    "id": "ls_001",
                    "firm_name": "广东广和律师事务所",
                    "city": "广州",
                    "practice_areas": ["公司并购", "投资基金", "争议解决", "税务法"],
                    "score": 4.5,
                    "founded": 1993,
                    "team_size": 150,
                    "profile": "广和律师事务所是华南地区领先的律师事务所，在公司并购和投资基金领域具有突出优势。",
                    "contacts": {
                        "telephone": "020-38391888",
                        "email": "<EMAIL>",
                        "office_address": "广州市天河区珠江新城珠江东路28号越秀金融大厦30楼",
                        "web": "www.grandall.com.cn"
                    }
                }
            ]
        }
        
        return self._format_api_data(mock_response["firms"], "法律服务API")
    
    def _fetch_from_law_firms_db(self) -> List[Dict[str, Any]]:
        """从律师事务所数据库API获取数据（模拟实现）"""
        LOG.info("正在从律师事务所数据库API获取数据...")
        
        mock_response = [
            {
                "firm_code": "lf_001",
                "firm_title": "浙江天册律师事务所",
                "region": "杭州",
                "expertise": ["互联网法", "电子商务", "数据保护", "创业投资"],
                "reputation": 4.8,
                "years_active": 25,
                "attorney_count": 180,
                "summary": "天册律师事务所是浙江省知名律师事务所，在互联网法和电子商务领域处于领先地位。",
                "contact_details": {
                    "phone_number": "0571-87901111",
                    "email_address": "<EMAIL>",
                    "street_address": "杭州市上城区西湖大道1号世贸中心A座11楼",
                    "homepage": "www.tclawfirm.com"
                }
            }
        ]
        
        return self._format_api_data(mock_response, "律师事务所数据库API")
    
    def _format_api_data(self, raw_data: List[Dict], source: str) -> List[Dict[str, Any]]:
        """格式化API数据为统一格式"""
        formatted_data = []
        
        for item in raw_data:
            try:
                # 根据不同的数据源格式进行转换
                if source == "律师目录API":
                    formatted_item = {
                        "id": item.get("firm_id"),
                        "name": item.get("name"),
                        "location": item.get("location"),
                        "specialties": item.get("specialties", []),
                        "rating": item.get("rating", 4.0),
                        "experience_years": 2024 - item.get("established_year", 2000),
                        "team_size": item.get("lawyers_count", 50),
                        "description": item.get("description", ""),
                        "contact": {
                            "phone": item.get("contact_info", {}).get("phone", ""),
                            "email": item.get("contact_info", {}).get("email", ""),
                            "address": item.get("contact_info", {}).get("address", ""),
                            "website": item.get("contact_info", {}).get("website", "")
                        },
                        "notable_cases": item.get("notable_cases", []),
                        "lawyers": item.get("key_lawyers", []),
                        "source": source
                    }
                elif source == "法律服务API":
                    formatted_item = {
                        "id": item.get("id"),
                        "name": item.get("firm_name"),
                        "location": item.get("city"),
                        "specialties": item.get("practice_areas", []),
                        "rating": item.get("score", 4.0),
                        "experience_years": 2024 - item.get("founded", 2000),
                        "team_size": item.get("team_size", 50),
                        "description": item.get("profile", ""),
                        "contact": {
                            "phone": item.get("contacts", {}).get("telephone", ""),
                            "email": item.get("contacts", {}).get("email", ""),
                            "address": item.get("contacts", {}).get("office_address", ""),
                            "website": item.get("contacts", {}).get("web", "")
                        },
                        "source": source
                    }
                else:  # 律师事务所数据库API
                    formatted_item = {
                        "id": item.get("firm_code"),
                        "name": item.get("firm_title"),
                        "location": item.get("region"),
                        "specialties": item.get("expertise", []),
                        "rating": item.get("reputation", 4.0),
                        "experience_years": item.get("years_active", 10),
                        "team_size": item.get("attorney_count", 50),
                        "description": item.get("summary", ""),
                        "contact": {
                            "phone": item.get("contact_details", {}).get("phone_number", ""),
                            "email": item.get("contact_details", {}).get("email_address", ""),
                            "address": item.get("contact_details", {}).get("street_address", ""),
                            "website": item.get("contact_details", {}).get("homepage", "")
                        },
                        "source": source
                    }
                
                formatted_data.append(formatted_item)
                
            except Exception as e:
                LOG.error(f"格式化数据失败: {str(e)}")
                continue
        
        return formatted_data
    
    def _get_mock_api_data(self) -> List[Dict[str, Any]]:
        """获取模拟API数据"""
        return [
            {
                "id": "mock_001",
                "name": "模拟律师事务所A",
                "location": "北京",
                "specialties": ["公司法", "合同法"],
                "rating": 4.5,
                "experience_years": 15,
                "team_size": 100,
                "description": "这是一个模拟的律师事务所数据，用于演示API功能。",
                "contact": {
                    "phone": "010-12345678",
                    "email": "<EMAIL>",
                    "address": "北京市朝阳区模拟地址123号",
                    "website": "www.mock-law.com"
                },
                "source": "模拟API数据"
            }
        ]
