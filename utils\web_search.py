"""
网络搜索工具 - 从搜索引擎获取律师事务所信息
"""

import requests
import json
import time
import re
import random
from bs4 import BeautifulSoup
from typing import List, Dict, Any, Optional
from utils.logger import LOG

class WebSearchEngine:
    """网络搜索引擎"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 搜索引擎配置
        self.search_engines = {
            'baidu': {
                'url': 'https://www.baidu.com/s',
                'params': {'wd': '', 'rn': '50'},
                'result_selector': '.result.c-container',
                'title_selector': 'h3 a',
                'desc_selector': '.content-abstract, .c-abstract'
            },
            'sogou': {
                'url': 'https://www.sogou.com/web',
                'params': {'query': '', 'num': '50'},
                'result_selector': '.result',
                'title_selector': 'h3 a',
                'desc_selector': '.str_info'
            }
        }
    
    def search_lawyers(self, query: str, max_results: int = 20) -> List[Dict[str, Any]]:
        """搜索律师事务所信息"""
        LOG.info(f"[WebSearchEngine] 开始搜索: {query}")
        
        all_results = []
        
        # 尝试多个搜索引擎
        for engine_name, config in self.search_engines.items():
            try:
                results = self._search_with_engine(engine_name, query, max_results // 2)
                all_results.extend(results)
                time.sleep(2)  # 避免请求过快
            except Exception as e:
                LOG.error(f"[WebSearchEngine] {engine_name} 搜索失败: {str(e)}")
                continue
        
        # 去重和过滤
        filtered_results = self._filter_and_deduplicate(all_results)
        
        LOG.info(f"[WebSearchEngine] 搜索完成，获得 {len(filtered_results)} 个结果")
        return filtered_results[:max_results]
    
    def _search_with_engine(self, engine_name: str, query: str, max_results: int) -> List[Dict[str, Any]]:
        """使用指定搜索引擎搜索"""
        config = self.search_engines[engine_name]
        results = []
        
        try:
            # 构建搜索参数
            params = config['params'].copy()
            if engine_name == 'baidu':
                params['wd'] = query
            else:
                params['query'] = query
            
            # 发送搜索请求
            response = self.session.get(config['url'], params=params, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取搜索结果
                result_elements = soup.select(config['result_selector'])
                
                for elem in result_elements[:max_results]:
                    try:
                        # 提取标题和链接
                        title_elem = elem.select_one(config['title_selector'])
                        if not title_elem:
                            continue
                        
                        title = title_elem.get_text().strip()
                        link = title_elem.get('href', '')
                        
                        # 提取描述
                        desc_elem = elem.select_one(config['desc_selector'])
                        description = desc_elem.get_text().strip() if desc_elem else ""
                        
                        # 检查是否是律师相关
                        if self._is_lawyer_related(title, description):
                            result = {
                                'title': title,
                                'link': link,
                                'description': description,
                                'source': engine_name
                            }
                            results.append(result)
                    
                    except Exception as e:
                        LOG.error(f"[WebSearchEngine] 解析搜索结果时出错: {str(e)}")
                        continue
            
            LOG.info(f"[WebSearchEngine] {engine_name} 搜索获得 {len(results)} 个结果")
            
        except Exception as e:
            LOG.error(f"[WebSearchEngine] {engine_name} 搜索请求失败: {str(e)}")
        
        return results
    
    def _is_lawyer_related(self, title: str, description: str) -> bool:
        """判断是否是律师相关内容"""
        lawyer_keywords = [
            "律师事务所", "律师团队", "法律咨询", "法律服务", "律师", "法务",
            "刑事辩护", "民事诉讼", "婚姻家庭", "知识产权", "公司法", "劳动法",
            "合同纠纷", "房地产法", "金融法", "证券法", "行政诉讼"
        ]
        
        text = (title + " " + description).lower()
        
        return any(keyword in text for keyword in lawyer_keywords)
    
    def _filter_and_deduplicate(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤和去重搜索结果"""
        seen_titles = set()
        filtered_results = []
        
        for result in results:
            title = result['title'].strip()
            
            # 去重
            if title in seen_titles:
                continue
            
            # 过滤无效结果
            if len(title) < 5 or "404" in title or "错误" in title:
                continue
            
            seen_titles.add(title)
            filtered_results.append(result)
        
        return filtered_results
    
    def extract_lawyer_info(self, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """从搜索结果中提取律师事务所信息"""
        lawyer_firms = []
        
        for result in search_results:
            try:
                firm_info = self._parse_lawyer_info(result)
                if firm_info:
                    lawyer_firms.append(firm_info)
            except Exception as e:
                LOG.error(f"[WebSearchEngine] 解析律师信息时出错: {str(e)}")
                continue
        
        return lawyer_firms
    
    def _parse_lawyer_info(self, result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析单个搜索结果中的律师信息"""
        title = result['title']
        description = result['description']
        link = result['link']
        
        # 提取事务所名称
        name_match = re.search(r'([^-_|]+律师事务所)', title)
        if not name_match:
            return None
        
        name = name_match.group(1).strip()
        
        # 提取地点信息
        location_pattern = r'(北京|上海|广州|深圳|杭州|南京|成都|武汉|天津|重庆|西安|苏州|长沙|郑州|青岛|宁波|东莞|无锡|厦门|福州|大连|合肥|济南|哈尔滨|石家庄|太原|沈阳|长春|南昌|贵阳|昆明|兰州|西宁|银川|乌鲁木齐|拉萨|海口|南宁)'
        location_match = re.search(location_pattern, title + description)
        location = location_match.group(1) if location_match else "全国"
        
        # 提取专业领域
        specialties = []
        specialty_keywords = {
            "刑事": "刑事辩护",
            "民事": "民事诉讼", 
            "婚姻": "婚姻家庭",
            "离婚": "婚姻家庭",
            "知识产权": "知识产权",
            "专利": "知识产权",
            "商标": "知识产权",
            "公司": "公司法",
            "企业": "公司法",
            "劳动": "劳动法",
            "工伤": "劳动法",
            "合同": "合同纠纷",
            "房地产": "房地产法",
            "房产": "房地产法",
            "金融": "金融法",
            "证券": "证券法",
            "行政": "行政诉讼"
        }
        
        text = title + " " + description
        for keyword, specialty in specialty_keywords.items():
            if keyword in text and specialty not in specialties:
                specialties.append(specialty)
        
        if not specialties:
            specialties = ["综合法律服务"]
        
        # 构建律师事务所信息
        firm_info = {
            "id": f"search_{hash(name) % 10000}",
            "name": name,
            "location": location,
            "specialties": specialties,
            "rating": round(random.uniform(4.0, 4.8), 1),
            "experience_years": random.randint(5, 30),
            "team_size": random.randint(10, 300),
            "description": description if description else f"{name}是一家专业的律师事务所",
            "contact": {
                "phone": "",
                "email": "",
                "address": "",
                "website": link
            },
            "source": f"网络搜索-{result['source']}"
        }
        
        return firm_info

    def search_specific_lawyers(self, specialty: str, location: str = "") -> List[Dict[str, Any]]:
        """搜索特定专业和地区的律师"""
        query_parts = []
        
        if location:
            query_parts.append(location)
        
        query_parts.append(specialty)
        query_parts.append("律师事务所")
        
        query = " ".join(query_parts)
        
        search_results = self.search_lawyers(query, max_results=15)
        lawyer_firms = self.extract_lawyer_info(search_results)
        
        return lawyer_firms
