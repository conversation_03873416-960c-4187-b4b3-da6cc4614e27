/* 场景选择器样式 */
.scenario-selector {
    margin-bottom: 2rem;
}

.scenario-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.scenario-option {
    cursor: pointer;
}

.scenario-option input[type="radio"] {
    display: none;
}

.scenario-card {
    padding: 2rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.scenario-card:hover {
    border-color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
}

.scenario-option input[type="radio"]:checked + .scenario-card {
    border-color: #1e3a8a;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
}

.scenario-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #b45309;
}

.scenario-option input[type="radio"]:checked + .scenario-card i {
    color: white;
}

.scenario-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.scenario-card p {
    color: #64748b;
    font-size: 0.9rem;
}

.scenario-option input[type="radio"]:checked + .scenario-card p {
    color: rgba(255, 255, 255, 0.9);
}

/* 场景介绍样式 */
.scenario-intro {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-left: 4px solid #b45309;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: #374151;
    line-height: 1.7;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.vocab-intro {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-left: 4px solid #b45309;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: #374151;
    line-height: 1.7;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 聊天容器样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    background: #ffffff;
    overflow: hidden;
    position: relative;
    flex: 1;
    min-height: 400px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    padding: 0;
    max-height: calc(100vh - 200px);
}

.welcome-message {
    text-align: center;
    color: #6e6e80;
    padding: 3rem 2rem;
    margin: auto;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.welcome-icon {
    font-size: 3rem;
    color: #10a37f;
    margin-bottom: 1rem;
}

.welcome-text {
    font-size: 1.1rem;
    line-height: 1.6;
}

/* 打字指示器 */
.typing-indicator {
    display: none;
}

.typing-dots {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 0;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #8e8ea0;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    40% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 消息样式 */
.message {
    display: flex;
    padding: 0.5rem 1.5rem;
    margin: 1rem 0; /* 统一上下边距 */
    animation: fadeInUp 0.3s ease;
    align-items: flex-end;
    gap: 0.5rem;
    clear: both;
    width: 100%;
}

.message.user {
    flex-direction: row-reverse;
    justify-content: flex-start;
}

.message.bot {
    flex-direction: row;
    justify-content: flex-start;
}

.message-wrapper {
    display: flex;
    flex-direction: column;
    max-width: 70%;
    min-width: 80px;
}

.message.user .message-wrapper {
    align-items: flex-end;
}

.message.bot .message-wrapper {
    align-items: flex-start;
}

.message-content {
    word-wrap: break-word;
    line-height: 1.5;
    font-size: 0.95rem;
    position: relative;
    display: inline-block;
    width: fit-content;
    white-space: pre-line; /* 保留换行符但不保留多余空格 */
    text-align: left; /* 确保左对齐 */
}

/* 头像样式 */
.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
    border: 2px solid #ffffff;
    margin-bottom: 4px;
}

.message.user .message-avatar {
    box-shadow: 0 1px 4px rgba(16, 163, 127, 0.2);
    border-color: #10a37f;
}

.message.bot .message-avatar {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    border-color: #f1f3f4;
}

.message-avatar.user-avatar {
    background: white;
    border-color: #10a37f;
}

.message-avatar.bot-avatar {
    background: white;
    border-color: #f0f0f0;
}

.message-avatar img {
    width: 28px;
    height: 28px;
    object-fit: cover;
    border-radius: 50%;
}

/* 用户消息内容样式 */
.message.user .message-content {
    color: #ffffff;
    background-color: #10a37f;
    border-radius: 18px 18px 4px 18px;
    padding: 8px 12px;
    box-shadow: 0 1px 3px rgba(16, 163, 127, 0.3);
    display: inline-block;
    max-width: 100%;
    min-width: 20px;
    word-break: break-word;
}

/* 机器人消息内容样式 */
.message.bot .message-content {
    color: #374151;
    background-color: #f1f3f4;
    border-radius: 18px 18px 18px 4px;
    padding: 8px 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    display: inline-block;
    max-width: 100%;
    min-width: 20px;
    word-break: break-word;
}

.message-time {
    font-size: 0.7rem;
    color: #9ca3af;
    margin-top: 2px;
    display: block;
    padding: 0 2px;
}

.message.user .message-time {
    text-align: right;
}

.message.bot .message-time {
    text-align: left;
}

/* 输入区域样式 */
.chat-input-container {
    border-top: 1px solid #f0f0f0;
    padding: 1.5rem 2rem;
    background: #ffffff;
    position: sticky;
    bottom: 0;
}

.chat-input-wrapper {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
    max-width: 768px;
    margin: 0 auto;
}

.chat-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #d9d9e3;
    border-radius: 12px;
    font-size: 1rem;
    outline: none;
    transition: all 0.2s ease;
    background: white;
    resize: none;
    min-height: 44px;
    max-height: 120px;
}

.chat-input:focus {
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.1);
}

.chat-input:disabled {
    background: #f1f5f9;
    color: #a0aec0;
    cursor: not-allowed;
}

.send-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 8px;
    background: #10a37f;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.send-btn:hover:not(:disabled) {
    background: #0e8f6f;
}

.send-btn:disabled {
    background: #d9d9e3;
    color: #8e8ea0;
    cursor: not-allowed;
}

.send-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: #a0aec0;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    color: #1e3a8a;
    background: #f7fafc;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* 设置表单样式 */
.settings-container {
    max-height: 70vh;
    overflow-y: auto;
    padding: 0.5rem;
}

.setting-section {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.setting-section:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e1;
}

.setting-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
}

.setting-title i {
    color: #1e3a8a;
}

.setting-group {
    margin-bottom: 1rem;
}

.setting-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 14px;
    padding: 2px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.input-wrapper input {
    flex: 1;
    padding: 1rem 3.5rem 1rem 1.25rem;
    border: 2px solid transparent;
    border-radius: 12px;
    font-size: 1rem;
    background: #ffffff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
}

/* 禁用浏览器默认的密码显示/隐藏按钮 */
.input-wrapper input::-ms-reveal,
.input-wrapper input::-ms-clear {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

.input-wrapper input::-webkit-credentials-auto-fill-button,
.input-wrapper input::-webkit-caps-lock-indicator {
    display: none !important;
    visibility: hidden !important;
    pointer-events: none !important;
}

/* 针对Edge和Chrome的额外隐藏规则 */
input[type="password"]::-ms-reveal {
    display: none !important;
}

input[type="password"]::-webkit-reveal {
    display: none !important;
}

/* 确保没有额外的padding被添加 */
.input-wrapper input[type="password"] {
    padding-right: 3.5rem !important;
}

/* 额外的浏览器兼容性规则 */
.input-wrapper input[type="password"]::-webkit-textfield-decoration-container {
    display: none !important;
}

.input-wrapper input[type="password"]::-webkit-inner-spin-button,
.input-wrapper input[type="password"]::-webkit-outer-spin-button {
    display: none !important;
}

/* Firefox 特定规则 */
.input-wrapper input[type="password"]::-moz-reveal {
    display: none !important;
}

/* 确保输入框在所有浏览器中都有一致的外观 */
.input-wrapper input[type="password"],
.input-wrapper input[type="text"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #667eea;
    background: #ffffff;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.input-wrapper:focus-within {
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15),
                0 8px 25px rgba(102, 126, 234, 0.1),
                0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.input-wrapper input::placeholder {
    color: #9ca3af;
    font-style: italic;
}

.toggle-password {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6b7280;
    font-size: 1.1rem;
    transition: color 0.2s ease;
    z-index: 2;
}

.toggle-password:hover {
    color: #374151;
}



.setting-hint {
    display: block;
    margin-top: 0.5rem;
    color: #6b7280;
    font-size: 0.85rem;
    line-height: 1.4;
    padding: 0.5rem 0.75rem;
    background: rgba(59, 130, 246, 0.05);
    border-left: 3px solid #3b82f6;
    border-radius: 0 4px 4px 0;
}

/* API Key 输入框特殊样式 */
.input-wrapper input[type="password"],
.input-wrapper input[type="text"] {
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
}

.input-wrapper input[type="password"]::placeholder,
.input-wrapper input[type="text"]::placeholder {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    letter-spacing: normal;
}

/* 添加输入框的微妙动画效果 */
.input-wrapper {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-wrapper:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

/* 确保在所有情况下都只显示我们的自定义眼睛图标 */
.input-wrapper {
    overflow: visible;
}

.input-wrapper input {
    position: relative;
    z-index: 1;
}

/* 强制隐藏所有可能的浏览器默认控件 */
input[type="password"] {
    -webkit-textfield-decoration-container: none !important;
}

input[type="password"]::-webkit-textfield-decoration-container {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

/* 头像选择器样式 */
.avatar-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.current-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.current-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.avatar-actions .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    white-space: nowrap;
}

/* 书籍选择器样式 */
.book-selector {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.book-selector h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
}

.book-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.book-option {
    cursor: pointer;
}

.book-option input[type="radio"] {
    display: none;
}

.book-card {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.book-card:hover {
    border-color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
}

.book-option input[type="radio"]:checked + .book-card {
    border-color: #1e3a8a;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    color: white;
}

.book-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #b45309;
}

.book-option input[type="radio"]:checked + .book-card i {
    color: #fbbf24;
}

.book-card h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.book-card p {
    font-size: 0.9rem;
    opacity: 0.8;
    line-height: 1.4;
}

.book-actions {
    text-align: center;
}

.book-actions .btn {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
}

/* 重新开始按钮样式 */
.restart-learning-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    border: 2px solid #1e3a8a;
    color: #1e3a8a;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.restart-learning-btn:hover {
    background: #1e3a8a;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}
    font-weight: 500;
    color: #4a5568;
}

.setting-group input:not(.input-wrapper input) {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    background: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.setting-group input:not(.input-wrapper input):focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
    background: #fefefe;
}

.setting-group input:not(.input-wrapper input)::placeholder {
    color: #9ca3af;
    font-style: italic;
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .scenario-options {
        grid-template-columns: 1fr;
    }
    
    .chat-container {
        height: calc(100vh - 120px);
    }

    .chat-input-container {
        padding: 1rem;
    }

    /* 移动端消息样式 */
    .message {
        padding: 0.5rem 1rem;
        margin: 0.75rem 0; /* 移动端统一上下边距 */
    }

    .message-wrapper {
        max-width: 85%;
    }

    .message-avatar {
        width: 28px;
        height: 28px;
    }

    .message-avatar img {
        width: 24px;
        height: 24px;
    }

    .message-content {
        font-size: 0.9rem;
        padding: 8px 10px;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
}

/* 文书生成样式 */
.document-generation-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

#documentGenerationChat {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    position: relative;
}

#documentGenerationChat .chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    padding-bottom: 80px; /* 为输入框留出空间 */
    margin-bottom: 0;
}

#documentGenerationChat .chat-input-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #e2e8f0;
    background: white;
    padding: 1rem;
    z-index: 1000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* 当文书生成标签页激活时，调整输入框位置 */
#document_generation-tab.active #documentGenerationChat .chat-input-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}

.template-selector {
    padding: 1rem;
    overflow-y: auto;
}

.template-categories {
    margin-top: 1rem;
}

.category-section {
    margin-bottom: 2rem;
}

.category-section h4 {
    color: #1e3a8a;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.template-grid {
    display: grid;
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0; /* 左对齐，不居中 */
}

/* 民事诉讼类别 - 4个模板在一行 */
#civilTemplates {
    grid-template-columns: repeat(4, 1fr);
}

/* 婚姻家庭类别 - 1个模板左对齐 */
#familyTemplates {
    grid-template-columns: 1fr;
    max-width: 300px;
    justify-self: start; /* 左对齐 */
}

/* 劳动争议类别 - 1个模板左对齐 */
#laborTemplates {
    grid-template-columns: 1fr;
    max-width: 300px;
    justify-self: start; /* 左对齐 */
}

.template-card {
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 220px; /* 固定高度确保一致性 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-width: 0; /* 确保flex子项可以收缩 */
}

.template-card:hover {
    border-color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.15);
}

.template-icon {
    font-size: 2.5rem;
    color: #b45309;
    margin-bottom: 1rem;
}

.template-card h5 {
    color: #1e3a8a;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    min-height: 2.5rem; /* 确保标题高度一致 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-card p {
    color: #64748b;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
    flex: 1; /* 让描述文本占用剩余空间 */
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.select-template-btn {
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    margin-top: auto; /* 确保按钮始终在底部 */
}

.chat-header {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selected-template-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #1e3a8a;
    font-weight: 500;
}

.selected-template-info i {
    color: #b45309;
}

.download-message .message-content {
    background: #f0f9ff;
    border-left: 4px solid #0ea5e9;
}

.download-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    text-align: center;
}

.download-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #0ea5e9;
    font-weight: 500;
}

.download-info i {
    font-size: 1.5rem;
}

.download-btn {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.download-btn:hover {
    background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .template-grid,
    #civilTemplates,
    #familyTemplates,
    #laborTemplates {
        grid-template-columns: 1fr;
        max-width: none;
    }
}

    .chat-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .download-section {
        gap: 0.75rem;
    }

    .download-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* 语音识别动态效果 */
.voice-recognition-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.voice-recognition-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-width: 300px;
    text-align: center;
}

.voice-wave-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    margin: 1rem 0;
}

.voice-wave {
    width: 4px;
    height: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    margin: 0 2px;
    border-radius: 2px;
    animation: voice-wave 1.2s ease-in-out infinite;
}

.voice-wave:nth-child(2) { animation-delay: 0.1s; }
.voice-wave:nth-child(3) { animation-delay: 0.2s; }
.voice-wave:nth-child(4) { animation-delay: 0.3s; }
.voice-wave:nth-child(5) { animation-delay: 0.4s; }
.voice-wave:nth-child(6) { animation-delay: 0.5s; }
.voice-wave:nth-child(7) { animation-delay: 0.4s; }
.voice-wave:nth-child(8) { animation-delay: 0.3s; }
.voice-wave:nth-child(9) { animation-delay: 0.2s; }
.voice-wave:nth-child(10) { animation-delay: 0.1s; }

@keyframes voice-wave {
    0%, 100% {
        height: 20px;
        opacity: 0.4;
    }
    50% {
        height: 50px;
        opacity: 1;
    }
}

.voice-recognition-text {
    color: #333;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.voice-recognition-hint {
    color: #666;
    font-size: 0.9rem;
    margin-top: 1rem;
}

.voice-recognition-timer {
    color: #999;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

/* 语音识别按钮激活状态 */
.voice-btn.recording {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    animation: pulse-recording 1.5s ease-in-out infinite;
}

@keyframes pulse-recording {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
}

/* 圆形聆听弹窗样式 */
#listeningModal .listening-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: white;
    text-align: center;
}

#listeningModal .listening-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: pulse 2s infinite;
}

#listeningModal .listening-text {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#listeningModal .listening-timer {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 1rem;
}

#listeningModal .listening-waves {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

#listeningModal .wave {
    width: 3px;
    height: 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 2px;
    animation: waveAnimation 1.5s ease-in-out infinite;
}

#listeningModal .wave:nth-child(1) { animation-delay: 0s; }
#listeningModal .wave:nth-child(2) { animation-delay: 0.2s; }
#listeningModal .wave:nth-child(3) { animation-delay: 0.4s; }

@keyframes waveAnimation {
    0%, 100% {
        height: 15px;
        opacity: 0.4;
    }
    50% {
        height: 35px;
        opacity: 1;
    }
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    to {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 语音识别加载动画 */
.voice-loading-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 0;
}

.voice-loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #3b82f6;
    animation: voiceLoadingBounce 1.4s ease-in-out infinite both;
}

.voice-loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.voice-loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.voice-loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes voiceLoadingBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 用户消息加载动画 */
.user-loading-indicator {
    opacity: 0.8;
}

.user-loading-dots {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 0;
}

.user-loading-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #6b7280;
    animation: userLoadingBounce 1.2s ease-in-out infinite both;
}

.user-loading-dots span:nth-child(1) { animation-delay: -0.24s; }
.user-loading-dots span:nth-child(2) { animation-delay: -0.12s; }
.user-loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes userLoadingBounce {
    0%, 80%, 100% {
        transform: scale(0.7);
        opacity: 0.4;
    }
    40% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}
