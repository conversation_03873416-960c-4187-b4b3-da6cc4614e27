#!/usr/bin/env python3
"""
简单测试律师推荐系统
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        from utils.logger import LOG
        print("✓ Logger 导入成功")
        
        from api.lawyer_data_api import LawyerDataAPI
        print("✓ LawyerDataAPI 导入成功")
        
        from utils.web_search import WebSearchEngine
        print("✓ WebSearchEngine 导入成功")
        
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        print("✓ LawyerRecommendationAgent 导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {str(e)}")
        return False

def test_lawyer_data_api():
    """测试律师数据API"""
    print("\n测试律师数据API...")
    
    try:
        from api.lawyer_data_api import LawyerDataAPI
        
        api = LawyerDataAPI()
        print("✓ LawyerDataAPI 实例化成功")
        
        # 测试获取律师数据
        lawyers = api.fetch_lawyers_from_api()
        print(f"✓ 获取到 {len(lawyers)} 个律师团队")
        
        if lawyers:
            first_lawyer = lawyers[0]
            print(f"  示例律师: {first_lawyer.get('name', 'N/A')}")
            print(f"  地点: {first_lawyer.get('location', 'N/A')}")
            print(f"  专业: {', '.join(first_lawyer.get('specialties', []))}")
            print(f"  来源: {first_lawyer.get('source', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 律师数据API测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_recommendation_agent():
    """测试推荐代理"""
    print("\n测试推荐代理...")
    
    try:
        from agents.lawyer_recommendation_agent import LawyerRecommendationAgent
        
        agent = LawyerRecommendationAgent()
        print("✓ LawyerRecommendationAgent 实例化成功")
        
        # 测试加载律师团队
        teams = agent.load_lawyer_teams()
        print(f"✓ 加载了 {len(teams)} 个律师团队")
        
        # 测试简单推荐
        test_request = "我需要处理离婚财产分割问题"
        response = agent.process_recommendation_request(test_request, "test_session")
        
        if "律师团队推荐结果" in response:
            print("✓ 推荐功能正常工作")
            print("  推荐结果预览:")
            lines = response.split('<br>')[:5]
            for line in lines:
                if line.strip():
                    print(f"    {line.strip()}")
        else:
            print("✗ 推荐结果格式异常")
            print(f"  响应: {response[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ 推荐代理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始简单测试...")
    
    tests = [
        ("基本导入", test_basic_imports),
        ("律师数据API", test_lawyer_data_api),
        ("推荐代理", test_recommendation_agent)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {str(e)}")
    
    print(f"\n{'='*50}")
    print("测试结果汇总")
    print('='*50)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n测试{'成功' if success else '失败'}")
